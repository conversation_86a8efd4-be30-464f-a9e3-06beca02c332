from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
import structlog
import logging
import time
from datetime import datetime
from typing import Any, Dict

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    handlers=[
        logging.FileHandler('api.log'),
        logging.StreamHandler()
    ]
)

# Configure structlog
structlog.configure(
    processors=[
        structlog.stdlib.add_log_level,
        structlog.stdlib.add_logger_name,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
    ],
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

class LoggingMiddleware(BaseHTTPMiddleware):
    async def get_request_body(self, request: Request) -> Dict[str, Any]:
        """Safely extract the request body"""
        try:
            return await request.json()
        except:
            return {}

    def get_client_ip(self, request: Request) -> str:
        """Extract client IP from request headers or client info"""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        return request.client.host

    def create_request_context(self, request: Request, client_ip: str) -> Dict[str, Any]:
        """Create a context dictionary for logging"""
        return {
            "client_ip": client_ip,
            "method": request.method,
            "path": request.url.path,
            "query_params": dict(request.query_params),
        }

    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        client_ip = self.get_client_ip(request)
        
        # Create base context for this request
        request_context = self.create_request_context(request, client_ip)
        request_log = logger.bind(**request_context)

        # Log request start
        request_log.info("request.started")

        # Get and log request body
        body = await self.get_request_body(request)
        if body:
            request_log = request_log.bind(body=body)
            request_log.info("request.body.received")

        try:
            # Process the request
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Add response context
            response_context = {
                "status_code": response.status_code,
                "process_time": f"{process_time:.4f}s"
            }
            
            # Log response with combined context
            request_log.bind(**response_context).info("request.completed")
            
            # Add custom headers
            response.headers["X-Process-Time"] = str(process_time)
            response.headers["X-Client-IP"] = client_ip
            
            return response
            
        except Exception as e:
            # Calculate time until error
            error_time = time.time() - start_time
            
            # Log error with full context
            error_context = {
                "error": str(e),
                "error_type": type(e).__name__,
                "process_time": f"{error_time:.4f}s"
            }
            
            request_log.bind(**error_context).error("request.failed")
            raise
