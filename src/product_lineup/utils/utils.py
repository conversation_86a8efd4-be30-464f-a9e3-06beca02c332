from typing import List
from sqlalchemy import create_engine
import pandas as pd

import numpy as np
import os
from dotenv import load_dotenv

load_dotenv()
BASE_DIRECTORY = os.getenv("BASE_DIRECTORY")


def get_numbers_of_categories(product_ids: List[int], database):
    local_engine = create_engine(
        f"sqlite:///{os.path.join(BASE_DIRECTORY, database, 'database.sqlite')}",
        echo=True,
    )
    conn = local_engine.connect()
    sql_product_id = "({})".format(", ".join(map(str, product_ids)))
    sql = f"select distinct(product_category_id) from product where product_id in {sql_product_id}"
    pcat_id = pd.read_sql_query(sql, conn).values[0].tolist()
    return pcat_id


def check_existence_of_product_ids(product_ids: List[int], database):
    sql_product_id = "({})".format(", ".join(map(str, product_ids)))
    local_engine = create_engine(
        f"sqlite:///{os.path.join(BASE_DIRECTORY, database, 'database.sqlite')}",
        echo=True,
    )
    conn = local_engine.connect()
    sql = f"select product_id from product where product_id in {sql_product_id}"
    pids = pd.read_sql_query(sql, conn)
    pids = pids.to_numpy().squeeze()
    pids = np.atleast_1d(pids)
    missing_pids = list(set(product_ids) - set(pids))
    return missing_pids


def check_existence_of_attribute_ids(attribute_ids: List[int], category, database):
    local_engine = create_engine(
        f"sqlite:///{os.path.join(BASE_DIRECTORY, database, 'database.sqlite')}",
        echo=True,
    )
    conn = local_engine.connect()
    sql = (
        f"select attribute_id from attribute_order where product_category_id={category}"
    )
    atrid = pd.read_sql_query(sql, conn).to_numpy().squeeze()
    missing_atrids = set(attribute_ids) - set(atrid)
    if len(attribute_ids) == len(missing_atrids):
        return -1
    return missing_atrids
