# product_lineup/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .routers import products
from .routers.products import _processors
from .utils.logger import LoggingMiddleware

import os
from dotenv import load_dotenv

load_dotenv()

DATABASE = os.getenv('DATABASE')


class ProductLineup:
    def __init__(self):
        self.app = FastAPI()
        self.setup_routers()
        self.setup_cors()

    def setup_routers(self):
        self.app.include_router(products.router)

    def setup_cors(self):
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            allow_headers=["*"],
        )
        self.app.add_middleware(LoggingMiddleware)
    
    async def lifespan(self, app: FastAPI):
        # Code in this block runs on startup
        await _processors[DATABASE].start()
        
        yield  # Runs the application
        
        # Code after yield runs on shutdown
        await _processors[DATABASE].stop()


def create_app():
    product_lineup = ProductLineup()
    return product_lineup.app
