import tracemalloc
import os
import psutil
from functools import wraps

def log_memory_v2(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        # Start tracemalloc for this process if not already started
        if not tracemalloc.is_tracing():
            tracemalloc.start()
        
        # Get process-specific memory info
        process = psutil.Process(os.getpid())
        start_memory = process.memory_info().rss / (1024 * 1024)  # MB
        
        # Execute function
        result = func(*args, **kwargs)
        
        # Get final memory stats
        end_memory = process.memory_info().rss / (1024 * 1024)
        _, peak = tracemalloc.get_traced_memory()
        peak_mb = peak / (1024 * 1024)
        
        # Calculate memory difference for this process
        memory_diff = end_memory - start_memory
        
        print(f"""
Process ID: {os.getpid()}
Start Memory: {start_memory:.2f} MB
End Memory: {end_memory:.2f} MB
Memory Difference: {memory_diff:.2f} MB
Peak Memory: {peak_mb:.2f} MB
        """)
        
        tracemalloc.reset_peak()
        
        # Return both the original result and the process-specific peak memory
        if isinstance(result, tuple):
            return (*result, peak_mb)
        else:
            return (result, peak_mb)
            
    return wrapper