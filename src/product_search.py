from contextai.Schema.document import Document
from contextai.Embeddings.api_embeddings import APIEmbeddingsToolkit
from contextai.VectorDatabase import FaissVectorDatabase
from contextsqlite import SQLiteDatabase
from contextml.SqlUtil import sql_utils
from contextai.VectorDatabase.pg_vectordb import PostgresEngine


from typing import Dict, List
from tqdm import tqdm
import pandas as pd
import numpy as np
import orj<PERSON>

def string_conversion(row) -> str:
    value = row['measure']
    value = str(value) if value % 1 else str(int(value))
    if row['unit_display']:
        value += f' {row["unit_display"]}'
    return value

class ProductLineupIndexer:
    def __init__(self, db: SQLiteDatabase, vdb: FaissVectorDatabase) -> None:
        """Initialise the indexes for attributes.

        Args:
            db (SQLiteDatabase): Database with a whole category.
            vdb (FaissVectorDatabase): Vector database with the unique terms for the category.
        """
        self.db = db
        self.vdb = vdb
        self.engine = PostgresEngine(host='**********', user='fmoldovan', password='PBV6U6IA', database='gp_mm_live_20240905')
    
    @property
    def attribute_id(self):
        """Attribute id table"""
        excel_data = pd.read_csv("/home/<USER>/workspace/FlaviusM/2894 - Attribute ID.csv")
        self.engine.df_to_table(excel_data, table_name="attributeid")
    
    @property
    def attribute_order(self):
        """Absolute order for the attributes.
        """
        first_table = self.engine.query("""SELECT 
                attribute_id,
                product_category_id,
                ROW_NUMBER() OVER (PARTITION BY product_category_id ORDER BY pg.pcat_group_id NULLS LAST, ag.attribute_group_id NULLS LAST, aag.display_order NULLS LAST) - 1 AS "order"
            FROM attributeid
            LEFT JOIN pcat_group AS pg USING(pcat_group)
            LEFT JOIN attr_attr_group AS aag USING(attribute_id)
            LEFT JOIN attribute_group AS ag USING(attribute_group_id)
            WHERE requirement IN ('Always required','Required', 'Optional') 
            ORDER BY pg.pcat_group_id NULLS LAST, ag.attribute_group_id NULLS LAST, product_category_id, aag.display_order NULLS LAST""")
        self.engine.df_to_table(first_table, table_name="attribute_order")
    
    @property
    def attribute_term_measure(self):
        """Attribute terms that containes measure"""
        df = self.engine.query("""
            SELECT a.attribute_id,
            a.name,
            pa.measure,
            mu.unit_display,
            a.type
            FROM product_attribute pa
            LEFT JOIN attribute a using(attribute_id)
            LEFT JOIN measure_unit mu using(measure_unit_id)
            WHERE a.type ilike '%measure'
            GROUP BY 1, 2, 3, 4, 5
            """)
        df['term'] = df.apply(string_conversion, axis=1)
        self.engine.df_to_table(df, table_name="attribute_term_measure")
        self.engine.execute("""
        CREATE VIEW attribute_term_with_name AS
        SELECT 
            at.*,
            a.name AS name
        FROM 
            attribute_term at
        LEFT JOIN 
            attribute a ON at.attribute_id = a.attribute_id;
        """)

    @property
    def attribute_term_extended(self):
        """Table with all the attributes"""
        attribute_term_extended = self.engine.query("""
        WITH inserted AS (
            INSERT INTO attribute_term_measure (attribute_id, term, name)
            SELECT attribute_id, term, name
            FROM attribute_term_with_name
            WHERE NOT EXISTS (
                SELECT 1
                FROM attribute_term_measure
                WHERE attribute_term_measure.attribute_id = attribute_term_with_name.attribute_id
                AND attribute_term_measure.term = attribute_term_with_name.term
            )
            RETURNING attribute_id, term, name
        )
        SELECT attribute_id, term, name FROM inserted
        UNION ALL
        SELECT attribute_id, term, name FROM attribute_term_measure;

        """)
        self.engine.df_to_table(attribute_term_extended, table_name="attribute_term_extended")
    
    def filter_function(self, document: Document) -> bool:
        """The filter function for adj calculation"""
        items = document.metadata 
        if items.get("attribute_id") == self.current_id:
            return True
        return False

    @property
    def create_adj_matrix(self) -> pd.DataFrame:
        """Adj matrix
        
        Returns:
            pd.DataFrame
        """
        # self.attribute_id
        # print("Done attribute_id")
        # self.attribute_order
        # print("Done attribute_order")
        # self.attribute_term_measure
        # print("Done attribute_term_measure")
        # self.attribute_term_extended
        # print("Done attribute_term_extended")

        attribute_ids = self.engine.query("select distinct attribute_id from attribute_term_extended")['attribute_id'].values.tolist()

        adj_storage = []

        for idx, atid in tqdm(enumerate(attribute_ids), total=len(attribute_ids)):
            self.current_id = atid
            attribute_term = self.engine.query(f"SELECT * FROM attribute_term_extended where attribute_id = {self.current_id}")['term'].values.tolist()
            
            if not attribute_term or atid in (364, 912):
                adj_storage.append({
                    "adjacency_matrix": [],
                    "attribute_id": self.current_id,
                })
                continue
            
            result = self.vdb.batch_search(attribute_term, top_k=len(attribute_term), filter_function=self.filter_function, index_only=False)
            
            if not any(result):
                adj_storage.append({
                    "adjacency_matrix": [],
                    "attribute_id": self.current_id,
                })
                continue
            
            term_to_position = {term: idx for idx, term in enumerate(attribute_term)}
            adj_matrix = [[0.0 for _ in range(len(attribute_term))] for _ in range(len(attribute_term))]
            
            for row, (term, scores) in enumerate(zip(attribute_term, result)):
                adj_matrix[row][row] = 1.0
                for item in scores:
                    if item["index"] in term_to_position:
                        col = term_to_position[item["index"]]
                        if row != col:
                            adj_matrix[row][col] = item.get("score", 0)
            
            serialized_data = orjson.dumps(adj_matrix).decode()
     
            adj_storage.append({
                "adjacency_matrix": serialized_data,
                "attribute_id": self.current_id,
            })

        adj_matrix_df = pd.DataFrame(adj_storage)
        print("Done adj_matrix")
        return adj_matrix_df
    
    def save_matrix(self):
        """Saving the matrix"""
        pass

    def create_tables(self):
        self.create_adj_matrix

if __name__ == "__main__":
    embeddings = APIEmbeddingsToolkit(base_url='http://127.0.0.1:5003')
    vdb = FaissVectorDatabase.from_exist(embeddings=embeddings, name='product_lineup_extraterms', vectordb_dir="/home/<USER>/workspace/FlaviusM")
    db = SQLiteDatabase('/mnt/data/machine_learning/nathan/contextml/datasets/product_lineup.db')
    indexer = ProductLineupIndexer(db, vdb)
    indexer.create_tables()