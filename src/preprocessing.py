from contextai.VectorDatabase import (
    FaissVectorDatabase,
    PostgresVectorDatabase,
    TableConfig,
)

from contextai.VectorDatabase.base_vectordb import BaseVectorDatabase
from contextai.Embeddings.base_embeddings import BaseEmbeddings
from contextai.Schema import Document
import pandas as pd
import numpy as np
import os
from typing import List, Dict, Optional, Any, Type
import json
import pickle as pkl
import gc
from sqlalchemy import Column, Integer
from sqlalchemy.ext.declarative import declarative_base


try:
    from contextml.SqlUtil.sql_engine import PostgresEngine

    ctxml_installed = True
except:
    ctxml_installed = False

import os
from dotenv import load_dotenv

load_dotenv()

ATTRIBUTE_ORDER_FILE = os.getenv("ATTRIBUTE_ORDER_FILE")
BASE_DIRECTORY = os.getenv("BASE_DIRECTORY")
Base = declarative_base()

unit_map = {
    "g": ("mass", 1),
    "kg": ("mass", 1000),
    "mg": ("mass", 0.001),
    "lb": ("mass", 453.592),
    "oz": ("mass", 28.3495),
    "c": ("temperature", lambda x: x + 273.15),
    "f": ("temperature", lambda x: (x - 32) * 5 / 9 + 273.15),
    "k": ("temperature", 1),
    "m": ("length", 1),
    "cm": ("length", 0.01),
    "mm": ("length", 0.001),
    "km": ("length", 1000),
}


def parse_and_normalize(documents):
    import re

    normalized_values = []
    target_type = None

    for doc in documents:
        text = doc.text.lower()
        match = re.search(r"(\d+(?:\.\d+)?)\s*([a-z°]+)", text)
        if not match:
            continue

        value = float(match.group(1))
        unit = match.group(2).strip("°")

        if unit not in unit_map:
            continue

        measure_type, converter = unit_map[unit]

        if target_type is None:
            target_type = measure_type
        elif measure_type != target_type:
            continue  # skip mismatched types

        normalized_value = (
            converter(value) if callable(converter) else value * converter
        )
        normalized_values.append(normalized_value)

    return normalized_values


class Product(Base):
    __tablename__ = "product"

    id = Column(Integer, primary_key=True)
    product_id = Column(Integer)
    product_category_id = Column(Integer)


class AttributeOrder(Base):
    __tablename__ = "attribute_order"

    id = Column(Integer, primary_key=True)
    attribute_id = Column(Integer)
    product_category_id = Column(Integer)


def string_conversion(row: Dict[str, Any]) -> str:
    """Helper function to convert measures into attribute terms.

    Args:
        row (Dict[str, Any]): Dataframe row containing the measure.

    Returns:
        str: Converted attribute term.
    """
    value = row["measure"]
    value = str(value) if value % 1 else str(int(value))
    if row["unit_display"]:
        value += f' {row["unit_display"]}'
    return value


def save_product_mapping(pcat_id, indices_pid, dir):
    filepath = os.path.join(dir, str(pcat_id) + ".pkl")
    with open(filepath, "wb") as f:
        pkl.dump(indices_pid, f)
    del indices_pid
    gc.collect()


class Preprocessor:

    def __init__(
        self,
        database: PostgresEngine,
        embeddings: Type[BaseEmbeddings],
        vdb: BaseVectorDatabase,
        skip_id_list: list = None,
        **kwargs,
    ) -> None:
        """Initialise the preprocessor with the database.

        Args:
            database (PostgresEngine): Database object.
            embeddings (Type[BaseEmbeddingsToolkit]): Embeddings toolkit.
            working_folder (str): The folder where every score matrix and mapping is gonna be stored.
            vdb_type (Literal['faiss', 'pgvector'], optional): Type of vector database used. Defaults to 'faiss'.
            skip_id_list (list, optional): Product category id to skip at the preprocessing. Defaults to None
        """
        self._database = database
        self._embeddings = embeddings
        self._vdb = vdb
        self._preprocessing_directory = os.path.join(
            BASE_DIRECTORY, database.credentials.get("database")
        )
        self._skip_attribute_ids = set()
        self.update_skip_list(set(skip_id_list))

        self.INFO_JSON_PATH = os.path.join(BASE_DIRECTORY, "info.json")
        self._load_info()

        self._table = kwargs.get("table")
        self._pg_database = kwargs.get("pg_database")

        self.SCORE_MATRIX_DIR = os.path.join(
            self._preprocessing_directory, "score_matrix"
        )
        self.PRODUCT_MAPPING_DIR = os.path.join(
            self._preprocessing_directory, "product_mapping"
        )
        self.VDB_DIR = os.path.join(self._preprocessing_directory, "faiss_vdb")
        self.SCORE_MATRIX_SIZE = os.path.join(
            self._preprocessing_directory, "score_matrix_size.json"
        )
        self.PCAT_ID_SIZE = os.path.join(
            self._preprocessing_directory, "pcat_id_size.json"
        )
        self.ATTRIBUTE_IDS_BY_PCAT = os.path.join(
            self._preprocessing_directory, "attribute_ids_by_pcat.json"
        )
        self.ATTRIBUTE_ID_SIZE = os.path.join(
            self._preprocessing_directory, "attribute_id_size.json"
        )

    @property
    def database(self) -> PostgresEngine:
        """Database object.

        Returns:
            PostgresEngine: Database object.
        """
        return self._database

    @property
    def embeddings(self) -> BaseEmbeddings:
        """Embeddings toolkit.

        Returns:
            BaseEmbeddingsToolkit: Embeddings toolkit.
        """
        return self._embeddings

    @property
    def preprocessing_directory(self) -> str:
        """The base folder where all the files are stored

        Returns:
            str: File path
        """
        return self._preprocessing_directory

    @property
    def skip_attribute_ids(self) -> set:
        """Get the current set of attribute IDs to skip."""
        return self._skip_attribute_ids

    def _load_info(self):
        """Load the info.json file."""
        if not os.path.exists(self.INFO_JSON_PATH):
            self._info = {}
        else:
            with open(self.INFO_JSON_PATH, "r") as f:
                self._info = json.load(f)

    def update_skip_list(self, skip_ids: list[int]) -> None:
        """
        Update the list of attribute IDs to skip.

        Args:
            skip_ids (list[int]): List of attribute IDs to skip
        """
        self._skip_attribute_ids.update(set(skip_ids))
        # Clear the cached table to force regeneration
        if self.check_table_exists(table_name="attribute_order", schema="report_data"):
            self.database.drop_table("report_data.attribute_order")
            if hasattr(self, "_attributes_by_category_table"):
                delattr(self, "_attributes_by_category_table")
        if self.check_table_exists(
            table_name="attribute_term_measure", schema="report_data"
        ):
            self.database.drop_table("report_data.attribute_term_measure")
            if hasattr(self, "_attribute_term_measure"):
                delattr(self, "_attribute_term_measure")
        if self.check_table_exists(
            table_name="attribute_term_extended", schema="report_data"
        ):
            self.database.drop_table("report_data.attribute_term_extended")
            if hasattr(self, "_attribute_term_extended"):
                delattr(self, "_attribute_term_extended")

    @property
    def attributes_by_category_table(self) -> str:
        """Table name of attribute order in their respective category.

        Returns:
            str: Table name of attribute order in their respective category.
        """
        if not hasattr(self, "_attributes_by_category_table"):
            if self.check_table_exists(
                table_name="attribute_order", schema="report_data"
            ):
                self._attributes_by_category_table = "report_data.attribute_order"
            else:
                self._attributes_by_category_table = self.database.create_table(
                    f"""
                SELECT *,
                    ROW_NUMBER() OVER(PARTITION BY product_category_id ORDER BY attribute_id) - 1 AS pcat_attribute_order
                FROM (
                    SELECT DISTINCT(attribute_id), product_category_id
                    FROM data_manager.attribute_group_attribute_field_all_cat_full_view
                    WHERE attribute_id NOT IN ({', '.join(map(str, self._skip_attribute_ids))})
                ) sub
                """,
                    schema="report_data",
                    table_name="attribute_order",
                )
        return self._attributes_by_category_table

    @property
    def attribute_term_measure(self) -> str:
        """Table name for attribute term measure.

        Returns:
            str: Table name for attribute term measure.
        """
        if not hasattr(self, "_attribute_term_measure"):
            if self.check_table_exists(
                table_name="attribute_term_measure", schema="report_data"
            ):
                self._attribute_term_measure = "report_data.attribute_term_measure"
            else:
                sql = f"""
                SELECT a.attribute_id,
                    a.name,
                    pa.measure,
                    mu.unit_display,
                    a.type
                FROM product_attribute pa
                LEFT JOIN attribute a using(attribute_id)
                LEFT JOIN measure_unit mu using(measure_unit_id)
                WHERE a.type ilike '%measure'
                AND pa.measure is not null
                AND attribute_id IN (SELECT DISTINCT attribute_id FROM {self.attributes_by_category_table})
                GROUP BY 1, 2, 3, 4, 5
                """
                am = self.database.query(sql)
                am["term"] = am.apply(string_conversion, axis=1)
                am = am[["attribute_id", "term", "type"]]
                self._attribute_term_measure = self.database.df_to_table(
                    am, schema="report_data", table_name="attribute_term_measure"
                )
        return self._attribute_term_measure

    @property
    def attribute_term_extended(self) -> str:
        """Table name for attribute term extended.

        Returns:
            str: Table name for attribute term extended.
        """
        if not hasattr(self, "_attribute_term_extended"):
            if self.check_table_exists(
                table_name="attribute_term_extended", schema="report_data"
            ):
                self._attribute_term_extended = "report_data.attribute_term_extended"
            else:
                sql = f"""
                        WITH ate as (
                            SELECT attribute_term_id, attribute_id, term, 'term' as type
                            FROM attribute_term
                            UNION
                            SELECT null as attribute_term_id, attribute_id, term, type
                            FROM {self.attribute_term_measure}
                            ORDER BY 2, 3
                        )
                        SELECT ROW_NUMBER() OVER (PARTITION BY ate.attribute_id ORDER BY ate.term) - 1 AS term_order,
                        ate.*
                        FROM ate
                        WHERE ate.attribute_id IN (SELECT DISTINCT attribute_id FROM {self.attributes_by_category_table})
                        """
                ate = self.database.query(sql)
                self._attribute_term_extended = self.database.df_to_table(
                    ate, schema="report_data", table_name="attribute_term_extended"
                )
        return self._attribute_term_extended

    def check_table_exists(self, table_name: str, schema: str = "public") -> bool:
        """Helper method to check if a table exists.

        Args:
            table_name (str): Name of the table.
            schema (str, optional): Schema that the table lives in. Defaults to 'public'.

        Returns:
            bool: Whether or not the table exists.
        """
        sql = f"SELECT * FROM information_schema.tables WHERE table_schema='{schema}' AND table_name='{table_name}'"
        return self.database.query(sql).shape[0] > 0

    @classmethod
    def with_pgvector(
        cls,
        database: PostgresEngine,
        embeddings: Type[BaseEmbeddings],
        work_directory: str,
        vector_database: "PostgresEngine",
        table_config: "TableConfig",
        skip_id_list: list = None,
        **kwargs,
    ):
        vdb = PostgresVectorDatabase(
            embeddings=embeddings, database=vector_database, table=table_config
        )

        instance = cls(
            database=database,
            embeddings=embeddings,
            vdb=vdb,
            work_directory=work_directory,
            skip_id_list=skip_id_list,
            *kwargs,
        )
        return instance

    @classmethod
    def with_faiss(
        cls,
        database: PostgresEngine,
        embeddings: Type[BaseEmbeddings],
        skip_id_list: list = None,
        **kwargs,
    ):

        work_directory = database.credentials.get("database")
        preprocessing_directory = os.path.join(BASE_DIRECTORY, work_directory)
        vdb_directory = os.path.join(preprocessing_directory, "faiss_vdb")
        vdb = FaissVectorDatabase(
            embeddings=embeddings, vdb_dir=vdb_directory, init_save=True
        )

        instance = cls(
            database=database,
            embeddings=embeddings,
            vdb=vdb,
            work_directory=work_directory,
            skip_id_list=skip_id_list,
            **kwargs,
        )
        return instance

    def _check_attribute_order_correctness(self):
        skip_ids = []
        unique_attribute_id = self.database.query(
            f"SELECT DISTINCT(attribute_id) from {self.attributes_by_category_table}"
        )
        for aid in unique_attribute_id["attribute_id"].values:
            print(f"Checking {aid}")
            count = self.database.query(
                f"SELECT COUNT(attribute_id) FROM {self.attribute_term_extended} WHERE attribute_id = {aid}"
            )
            if count.values == 0:
                skip_ids.append(int(aid))
        print(f"Ids to skip: {skip_ids}")
        sql_skip_ids = "({})".format(", ".join(map(str, skip_ids)))
        self.skip_pcat_ids = self.database.query(
            f"""
            SELECT DISTINCT p.product_category_id 
            FROM product p
            JOIN attribute_order ao ON p.product_category_id = ao.product_category_id
            WHERE p.product_category_id IN (
                SELECT product_category_id
                FROM attribute_order
                GROUP BY product_category_id
                HAVING COUNT(CASE WHEN attribute_id NOT IN {sql_skip_ids} THEN 1 ELSE NULL END) = 0
            )
            """
        )["product_category_id"].to_list()
        self.update_skip_list(skip_ids)

    def _create_vectordb(self) -> None:
        """Creating the vector database for attribute terms."""
        import time

        # Grab the terms
        term_df = self.database.query(
            f"SELECT attribute_id, term, term_order, type FROM {self.attribute_term_extended}"
        )
        terms = term_df["term"].values
        metadatas = term_df[["attribute_id", "term_order", "type"]].to_dict("records")
        docs = [Document(text=x[0], metadata=x[1]) for x in zip(terms, metadatas)]
        print("Inserting attribute terms into vector database...", end="")
        start = time.perf_counter()
        self._vdb.add_documents(docs=docs, split_text=False)
        end = time.perf_counter() - start
        print(f"done; Time taken: {end:.2f}s.")

    def _calculate_score_matrix(
        self, attribute_id: int, measure_type: str, batch_size: int = 1000
    ) -> np.ndarray:
        """Creating the score matrix of the given attribute ID.

        Args:
            attribute_id (int): Attribute ID.
            batch_size (int, optional): Batch size of calculating scores. Defaults to 1000.

        Returns:
            np.ndarray: A square matrix with the scores. Index of the terms can be found in the attribute_term_extended table.
        """
        from tqdm import tqdm

        docs = self._vdb.search_by_doc(attribute_id=attribute_id, ids_only=False)
        # create empty score matrix
        doc_ids = np.array(list(docs.keys()))
        size = doc_ids.shape[0]
        score_matrix = np.zeros(shape=(size, size))

        # create arrays for doc information
        doc_arr = np.array(list(docs.values()))
        term_orders = np.vectorize(lambda x: x.metadata["term_order"])(doc_arr)
        texts = np.vectorize(lambda x: x.text)(doc_arr)
        doc_orders = np.argsort(term_orders)
        texts = texts[doc_orders]
        doc_arr = doc_arr[doc_orders]
        doc_ids = doc_ids[doc_orders]
        term_orders = term_orders[doc_orders]

        # Gather the terms and their vectors

        # Calculating batches
        num_batch = (
            size // batch_size
            if (size // batch_size) == (size / batch_size)
            else (size // batch_size) + 1
        )
        batches = [
            (b * batch_size, min(size, (b + 1) * batch_size)) for b in range(num_batch)
        ]

        # doing batch search on vector database
        index_map = dict(zip(doc_ids, term_orders))
        print(f"Creating score matrix for attribute id {attribute_id}...")
        for batch in tqdm(batches):
            if measure_type == "measure":
                values = parse_and_normalize(batch)
                n = len(values)
                matrix = np.ones((n, n))

                for i in range(n):
                    for j in range(i + 1, n):
                        sim = min(values[i] / values[j], values[j] / values[i])
                        matrix[i, j] = matrix[j, i] = sim
                return score_matrix
            else:
                vectors = self._vdb.get_vectors_by_ids(
                    doc_ids[batch[0] : batch[1]]
                ).astype(dtype=np.float32)
                ids, scores = self._vdb._batch_search_by_vectors(
                    vectors=vectors, scope_ids=doc_ids, top_k=size
                )
                del vectors
                # Get indexes of results in the score matrix
                get_index = np.vectorize(lambda x: index_map[x])
                indexes = get_index(ids)
                first_indexes = np.arange(batch[0], batch[1])
                first_indexes = np.tile(
                    first_indexes.reshape(-1, 1), (1, scores.shape[1])
                )
                score_matrix[first_indexes, indexes] = scores
        score_matrix = (score_matrix + score_matrix.T) / 2
        return score_matrix

    def _create_score_matrices(
        self, batch_size: int = 1000, skip_ids: Optional[List[int]] = None
    ) -> None:
        """Creating score matrices and store them as json strings in a pickle file.
        Args:
            batch_size (int, optional): Batch size for processing scores. Defaults to 1000.
            skip_ids (Optional[List[int]], optional): Attribute IDs to skip. Defaults to None.
        """
        import pickle
        import os

        # Counts of terms by attribute ID
        att_summary = self.database.query(
            f"""
                SELECT attribute_id,
                    count(term) as size,
                    type
                FROM {self.attribute_term_extended}
                GROUP BY 1
                ORDER BY 2 DESC
                """
        ).values
        # Create score matrix directory
        os.makedirs(self.SCORE_MATRIX_DIR)
        print("Score matrix folder has been freshly created.")
        # Insert score matrix
        score_matrix_size = {}
        skip_ids = skip_ids if skip_ids is not None else []
        for attribute_id, size, measure_type in att_summary:
            if attribute_id not in skip_ids:
                score_matrix = self._calculate_score_matrix(
                    attribute_id=attribute_id,
                    batch_size=batch_size,
                    measure_type=measure_type,
                )
                if np.all(score_matrix.sum(axis=1) == 0):
                    raise Warning(f"The score matrix {score_matrix} has all values 0")
                if not np.array_equal(score_matrix, score_matrix.T):
                    raise Warning(f"The score matrix {score_matrix} is not symetrical")
                if not np.all(score_matrix.diagonal()) == 1:
                    raise Warning(
                        f"The score matrix {score_matrix} has diagonal value different from one"
                    )
                if np.any(np.isnan(score_matrix)):
                    raise Warning(
                        f"The score matrix {score_matrix} containes NaN values"
                    )
                with open(f"{self.SCORE_MATRIX_DIR}/{attribute_id}.pkl", "wb") as f:
                    pickle.dump(score_matrix, f)
                score_matrix_size[int(attribute_id)] = int(size)
                del score_matrix
        with open(f"{self.SCORE_MATRIX_SIZE}", "w") as f:
            json.dump(score_matrix_size, f)

    def _create_products_id(self, product_category_id: int) -> pd.DataFrame:
        """Create the product id mapping group by product_category
        Args:
            product_cateogry_id (int): The product category grouped by

        Returns:
            pd.DataFrame: Dataframe with the product id and a list of indexes.
        """
        print(f"Creating mapping for product category id {product_category_id}...")
        df = self.database.query(
            f"""with f as (       
            SELECT p.product_id,
            p.product_category_id,
            ao.attribute_id,
            a.type,
            ao.pcat_attribute_order,
            pa.product_attribute_id,
            CASE 
            WHEN a.type = 'term' THEN at.term 
            WHEN pa.measure IS NOT NULL AND mu.unit_display IS NOT NULL THEN CONCAT(pa.measure, ' ', mu.unit_display)
            ELSE pa.measure::TEXT
            END as term
            FROM public.product p
            LEFT JOIN report_data.attribute_order ao using(product_category_id)
            LEFT JOIN public.product_attribute pa ON (p.product_id=pa.product_id AND ao.attribute_id = pa.attribute_id)
            LEFT JOIN public.attribute a ON (ao.attribute_id=a.attribute_id)
            LEFT JOIN public.measure_unit mu using(measure_unit_id)
            LEFT JOIN public.attribute_term at ON (pa.attribute_term_id=at.attribute_term_id)
            WHERE p.product_category_id = {product_category_id}
            ORDER BY p.product_id, p.product_category_id, ao.pcat_attribute_order
            ), max_sizes AS (
            SELECT ate.attribute_id, MAX(ate.term_order) + 1 as num_terms
            FROM report_data.attribute_term_extended ate
            GROUP BY ate.attribute_id
            )
            SELECT f.*, 
            ate.term_order,
            ms.num_terms
            FROM f
            LEFT JOIN report_data.attribute_term_extended ate ON (f.term = ate.term AND ate.attribute_id = f.attribute_id)
            LEFT JOIN max_sizes ms ON (f.attribute_id=ms.attribute_id)
            ORDER BY f.product_id, f.product_category_id, f.pcat_attribute_order;
            """
        )
        df["term_order_final"] = df["term_order"].fillna(df["num_terms"])
        df.drop_duplicates(["attribute_id", "product_id"], keep="first", inplace=True)
        ptm = df.groupby(by="product_id")["term_order_final"].apply(
            lambda x: np.array(x)
        )
        return ptm

    def _calculate_product_category_id_size(self) -> None:
        nof_products_id = {}
        pcat_ids = (
            self.database.query("select Distinct(product_category_id) from product")[
                "product_category_id"
            ]
            .dropna()
            .to_list()
        )
        for id in pcat_ids:
            count = self.database.query(
                f"select COUNT(product_id) from product where product_category_id = {id}"
            )["count"].to_list()[0]
            nof_products_id[int(id)] = count
        with open(self.PCAT_ID_SIZE, "w") as f:
            json.dump(nof_products_id, f)

    def _attribute_ids_by_pcat(self) -> None:
        sql = "SELECT product_category_id, attribute_id, pcat_attribute_order FROM report_data.attribute_order ORDER BY 1, 3"
        ao = self.database.query(sql)[["product_category_id", "attribute_id"]]
        attribute_ids_by_pcat = (
            ao.groupby("product_category_id")["attribute_id"]
            .apply(lambda x: list(x))
            .to_dict()
        )

        with open(self.ATTRIBUTE_IDS_BY_PCAT, "w") as f:
            json.dump(attribute_ids_by_pcat, f)

    def _attribute_ids_size(self) -> None:
        sql = """SELECT attribute_id, COUNT(*) as attribute_size
                    FROM attribute_term_extended 
                    GROUP BY attribute_id 
                    ORDER BY MIN(term_order);
                """
        df = self.database.query(sql)
        attribute_ids_size = dict(zip(df["attribute_id"], df["attribute_size"]))
        with open(self.ATTRIBUTE_ID_SIZE, "w") as f:
            json.dump(attribute_ids_size, f)

    def _compute_product_mapping(self):
        """Creating product id attributes index and store them in a table"""
        from threading import Thread

        self._create_score_matrices(skip_ids=self.skip_attribute_ids)
        with open(f"{self.SCORE_MATRIX_SIZE}", "r") as f:
            size = json.load(f)
            self.attribute_sizes = {int(k): v for k, v in size.items()}
            del size
        if self.skip_pcat_ids:
            skip_pcat_ids = "({})".format(", ".join(map(str, self.skip_pcat_ids)))
        pcategory_id = self.database.query(
            f"""SELECT product_category_id
                FROM report_data.attribute_order
                WHERE product_category_id NOT IN {skip_pcat_ids}
                GROUP BY product_category_id;"""
        )["product_category_id"].to_list()
        os.makedirs(self.PRODUCT_MAPPING_DIR)
        print("Score product mapping has been freshly created.")

        for pcat_id in pcategory_id:
            mapped_products = self._create_products_id(pcat_id)
            if not mapped_products.empty:
                raw_term_indices = np.array(
                    mapped_products.values.tolist(), dtype=np.int32
                )
                product_ids = mapped_products.index.values
                indices_pid = (raw_term_indices, product_ids)

                # Saving the product mapping to a pkl file
                trds = [
                    Thread(
                        target=save_product_mapping,
                        args=(pcat_id, indices_pid, self.PRODUCT_MAPPING_DIR),
                    )
                ]
                [trd.start() for trd in trds]
                [trd.join() for trd in trds]
                del mapped_products, product_ids, indices_pid
                gc.collect()

    def _copy_tables_for_inference(self):
        """For the inference we need to copy the attribute_order and product tables"""
        from sqlalchemy import create_engine
        from sqlalchemy import MetaData, select

        source_engine = create_engine(self.database.uri)
        metadata = MetaData()
        metadata.reflect(bind=source_engine)

        local_database_path = os.path.join(
            self._preprocessing_directory, "database.sqlite"
        )
        local_engine = create_engine(f"sqlite:///{local_database_path}", echo=True)
        Base.metadata.create_all(local_engine)

        # coying product table
        product_table = metadata.tables["product"]
        with source_engine.connect() as conn:
            products = conn.execute(select(product_table)).fetchall()

        with local_engine.begin() as conn:
            conn.execute(
                Product.__table__.insert(), [dict(row._mapping) for row in products]
            )

        # copying attribut order table
        attribute_order_table = metadata.tables["attribute_order"]
        with source_engine.connect() as conn:
            attribute_order = conn.execute(select(attribute_order_table)).fetchall()

        with local_engine.begin() as conn:
            conn.execute(
                AttributeOrder.__table__.insert(),
                [dict(row._mapping) for row in attribute_order],
            )

    def run_preprocessing(self):
        self._check_attribute_order_correctness()
        self._create_vectordb()
        self._attribute_ids_size()
        self._attribute_ids_by_pcat()
        self._calculate_product_category_id_size()
        self._compute_product_mapping()
        self._copy_tables_for_inference()
