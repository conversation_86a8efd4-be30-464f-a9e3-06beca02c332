try:
    from contextml.SqlUtil.sql_engine import PostgresEngine
    ctxml_installed = True
except:
    ctxml_installed = False

import requests

attribute_id_query = '''with f as (       
            SELECT p.product_id,
            p.product_category_id,
            ao.attribute_id,
            a.type,
            ao.pcat_attribute_order,
            pa.product_attribute_id,
            CASE 
            WHEN a.type = 'term' THEN at.term 
            WHEN pa.measure IS NOT NULL AND mu.unit_display IS NOT NULL THEN CONCAT(pa.measure, ' ', mu.unit_display)
            ELSE pa.measure::TEXT
            END as term
            FROM public.product p
            LEFT JOIN report_data.attribute_order ao using(product_category_id)
            LEFT JOIN public.product_attribute pa ON (p.product_id=pa.product_id AND ao.attribute_id = pa.attribute_id)
            LEFT JOIN public.attribute a ON (ao.attribute_id=a.attribute_id)
            LEFT JOIN public.measure_unit mu using(measure_unit_id)
            LEFT JOIN public.attribute_term at ON (pa.attribute_term_id=at.attribute_term_id)
            WHERE p.product_category_id = {0} and p.product_id = {1}
            ORDER BY p.product_id, p.product_category_id, ao.pcat_attribute_order
            ), max_sizes AS (
            SELECT ate.attribute_id, MAX(ate.term_order) + 1 as num_terms
            FROM report_data.attribute_term_extended ate
            GROUP BY ate.attribute_id
            )
            SELECT f.*, 
            ate.term_order,
            ms.num_terms
            FROM f
            LEFT JOIN report_data.attribute_term_extended ate ON (f.term = ate.term AND ate.attribute_id = f.attribute_id)
            LEFT JOIN max_sizes ms ON (f.attribute_id=ms.attribute_id)
            ORDER BY f.product_id, f.product_category_id, f.pcat_attribute_order;
            '''
pg = PostgresEngine(host="*************", user="fmoldovan", password='PBV6U6lA', port='5432', database="gp_mm_live_20241117")
if __name__ == "__main__":
    pcat_ids = pg.query("select distinct(product_category_id) from attribute_order").values.squeeze()

    url = "http://localhost:8001/get-similar-products-by-product-id"

    for pcat_id in pcat_ids:
        print(pcat_id)
        try:
            pid = pg.query(f"select product_id from product where product_category_id = {pcat_id} limit 1").values[0][0]
            payload = {
                "query_product_ids": [int(pid)],
                "database": "gp_mm_live_20241117",
                "top_k": 5,
            }
            response = requests.post(url, json=payload)
            json_content = response.json()
            product_category_id = pcat_id
            received_atrids = json_content['metadata']['attribute_id_order']
            test_atrids = pg.query(attribute_id_query.format(pcat_id, int(pid)))['attribute_id'].values.squeeze()
            if not set(received_atrids) == set(test_atrids):
                print("Something went wrong")
        except:
            pass