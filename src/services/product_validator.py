from src.models.schemas import ValidationResult


class ProductQueryValidator:
    def __init__(self, data: dict, pids, product_category_id):
        self.data = data
        self.result = ValidationResult()
        self.query_ids = data["query_ids"]
        self.scope_ids = data.get("scope_ids", [])
        self.weights = data.get("weights", {})
        self.top_k = data.get("top_k")
        self.category_id = None
        self.pids = pids
        self.product_category_id = product_category_id

    def validate(self) -> ValidationResult:
        """Validate product query parameters using optimized lookups"""

        self.validate_product_ids()

        # Check if weights are valid for the category
        # self.check_weights_valid_for_category()
        # if self.has_error():
        #     return self.result

        # # Check if query IDs have attributes specified in weights
        # self.check_query_ids_have_attrs()
        # if self.has_error():
        #     return self.result

        # # Run warning checks
        # self.run_warnings()

        return self.result

    def has_error(self) -> bool:
        return bool(self.result.errors)

    def check_weights_valid_for_category(self):
        valid_attrs = self.product_category_id
        invalid = [attr for attr in self.weights.keys() if attr not in valid_attrs]
        if invalid:
            self.result.add_error(
                f"Attributes in weights not exist for {self.category_id}"
            )

    def check_query_ids_have_attrs(self):
        for pid in self.query_ids:
            attrs = self.product_category_id
            if not any(attr in attrs for attr in self.weights.keys()):
                self.result.add_error(
                    "Query product IDs have no values for given attributes"
                )
                return

    def run_warnings(self):
        self.warn_topk_larger_than_scope()

    def warn_topk_larger_than_scope(self):
        if self.top_k and len(self.scope_ids) < self.top_k:
            self.result.add_warning("Top K larger than the size of scope IDs")

    def validate_product_ids(self) -> ValidationResult:
        """
        Efficiently validate product IDs and categories in a single pass using NumPy
        Handles errors:
            Query product IDs not in same category
            Query product IDs not exist
            No product IDS in scope
            Product IDs in scope not in same category as queries
        """
        import numpy as np

        if self.scope_ids:
            cids = np.concatenate([self.query_ids, self.scope_ids])
        else:
            cids = self.query_ids

        query_indices = np.where((cids[:, None] == self.pids))[1]
        ccats = self.product_category_id[query_indices]

        if not np.all(ccats):
            self.result.add_error("Query product IDs not exist")
            return

        # # Check if any query IDs are missing
        # if not np.all(ccats[: len(self.query_ids) - 1]):
        #     result.add_error("Query product IDs not exist")
        #     return result

        # # Check if query IDs are in the same category
        # if len(np.unique(ccats[: len(self.query_ids) - 1])) > 1:
        #     result.add_error("Query product IDs not in same category")
        #     return result

        return ccats
