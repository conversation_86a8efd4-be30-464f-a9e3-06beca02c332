from contextdb import PostgresEngine
from contextai.Schema import Document
from contextai.Embeddings.api_embeddings import APIEmbeddings
from typing import List, Optional, Literal, TYPE_CHECKING
import numpy as np
import pandas as pd
import pickle
import json
import os
import pickle as pkl
import gc

if TYPE_CHECKING:
    from contextai.Embeddings.base_embeddings import BaseEmbeddings
    from contextai.VectorDatabase.base_vectordb import BaseVectorDatabase
    from contextdb.Engine.base_database import BaseDatabaseEngine

CAT_ATTRIBUTE_TABLE = "data_manager.attribute_group_attribute_field_all_cat_full_view"


def save_product_mapping(pcat_id, indices_pid, dir):
    filepath = os.path.join(dir, str(pcat_id) + ".pkl")
    with open(filepath, "wb") as f:
        pkl.dump(indices_pid, f)
    del indices_pid
    gc.collect()


class Preprocessor:

    def __init__(
        self,
        database: "BaseDatabaseEngine",
        embeddings: "BaseEmbeddings",
        base_dir: str,
        vdb_type: Literal["faiss", "postgres"] = "faiss",
        skip_id_list: Optional[List[int]] = None,
        **kwargs,
    ) -> None:
        self._base_dir = base_dir
        self._database = database
        self._embeddings = embeddings
        self._vdb_type = vdb_type
        self._skip_id_list = skip_id_list or []
        self._update_skip_ids()

    @property
    def base_dir(self) -> str:
        return self._base_dir

    @property
    def database(self) -> "BaseDatabaseEngine":
        return self._database

    @property
    def embeddings(self) -> "BaseEmbeddings":
        return self._embeddings

    @property
    def preprocessing_dir(self) -> str:
        import os

        return os.path.join(self.base_dir, self.database.database)

    @property
    def vdb_dir(self) -> str:
        import os

        return os.path.join(self.preprocessing_dir, "faiss_vdb")

    @property
    def score_matrix_dir(self) -> str:
        import os

        return os.path.join(self.preprocessing_dir, "score_matrix")

    @property
    def attribute_ids_size_json(self) -> str:
        import os

        return os.path.join(self.preprocessing_dir, "attribute_ids_size.json")

    @property
    def attribute_ids_by_pcat_json(self) -> str:
        import os

        return os.path.join(self.preprocessing_dir, "attribute_ids_by_pcat.json")

    @property
    def pcat_id_size_json(self) -> str:
        import os

        return os.path.join(self.preprocessing_dir, "pcat_id_size.json")

    @property
    def score_matrix_size_json(self) -> str:
        import os

        return os.path.join(self.preprocessing_dir, "score_matrix_size.json")

    @property
    def product_mapping_dir(self) -> str:
        import os

        return os.path.join(self.preprocessing_dir, "product_mapping")

    @property
    def vdb(self) -> "BaseVectorDatabase":
        if not hasattr(self, "_vdb"):
            if self._vdb_type == "faiss":
                from contextai.VectorDatabase.faiss_vectordb import FaissVectorDatabase
                import os

                self._vdb = FaissVectorDatabase(
                    embeddings=self.embeddings, split_text=False, vdb_dir=self.vdb_dir
                )
            else:
                # For PGVector, needs to be refactored
                pass
        return self._vdb

    @property
    def skip_ids(self):
        return self._skip_id_list

    @property
    def sqlite_path(self):
        import os

        return os.path.join(self.preprocessing_dir, "sqlite")

    @property
    def attributes_by_category_table(self) -> str:
        """Table name of attribute order in their respective category.

        Returns:
            str: Table name of attribute order in their respective category.
        """
        table_name = "attribute_order_test_v1"
        if not hasattr(self, "_attributes_by_category_table"):
            if self.check_table_exists(table_name=table_name, schema="report_data"):
                self._attributes_by_category_table = f"report_data.{table_name}"
            else:
                ids_sql = (
                    ", ".join(map(str, self.skip_ids)) if self.skip_ids else "NULL"
                )
                self._attributes_by_category_table = self.database.create_table(
                    f"""
                SELECT *,
                    ROW_NUMBER() OVER(PARTITION BY product_category_id ORDER BY attribute_id) - 1 AS pcat_attribute_order
                FROM (
                    SELECT DISTINCT(attribute_id), product_category_id
                    FROM {CAT_ATTRIBUTE_TABLE}
                    WHERE attribute_id NOT IN ({ids_sql})
                ) sub
                """,
                    schema="report_data",
                    table_name=table_name,
                )
        return self._attributes_by_category_table

    def check_table_exists(self, table_name: str, schema: str = "public") -> bool:
        """Helper method to check if a table exists.

        Args:
            table_name (str): Name of the table.
            schema (str, optional): Schema that the table lives in. Defaults to 'public'.

        Returns:
            bool: Whether or not the table exists.
        """
        sql = f"SELECT * FROM information_schema.tables WHERE table_schema='{schema}' AND table_name='{table_name}'"
        return self.database.query(sql).shape[0] > 0

    def save_query_to_sqlite(self, postgres_uri, sqlite_path, database_name, query):
        from sqlalchemy import create_engine, text

        pg_engine = create_engine(postgres_uri)
        df = pd.read_sql_query(text(query), pg_engine)

        sqlite_engine = create_engine(f"sqlite:///{sqlite_path}/{database_name}")
        df.to_sql(database_name, sqlite_engine, index=False, if_exists="replace")

    def create_local_engine(self, sqlite_path, database_name):
        from sqlalchemy import create_engine

        local_engine = create_engine(f"sqlite:///{sqlite_path}/{database_name}")
        conn = local_engine.connect()
        return conn

    def _update_skip_ids(self) -> None:
        # Fetch measure attributes and their unit/term counts
        measure_skip = self.database.query(
            f"""
            WITH attrs AS (
                SELECT 
                    cat.attribute_id,
                    at.type AS attribute_type
                FROM {CAT_ATTRIBUTE_TABLE} cat
                LEFT JOIN public.attribute at USING(attribute_id)
                WHERE at.type IN ('measure', 'simple_measure')
                GROUP BY 1, 2
            ),
            cats AS (
                SELECT DISTINCT product_category_id
                FROM {CAT_ATTRIBUTE_TABLE}
            ),
            ub AS (
                SELECT 
                    mu.measure_id,
                    mu.unit AS base_unit
                FROM public.measure_unit mu
                WHERE mu.base
            ),
            pbase AS (
                SELECT 
                    pa.attribute_id,
                    attrs.attribute_type,
                    CASE 
                        WHEN mu.base_multiplier IS NOT NULL THEN pa.measure * mu.base_multiplier
                        ELSE pa.measure 
                    END AS normalised_measure,
                    COALESCE(ub.base_unit, mu.unit) AS base_unit
                FROM public.product_attribute pa
                LEFT JOIN public.measure_unit mu USING(measure_unit_id)
                LEFT JOIN ub USING(measure_id)
                LEFT JOIN public.product p USING(product_id)
                JOIN cats USING(product_category_id)
                JOIN attrs ON pa.attribute_id = attrs.attribute_id
            ),
            uc AS (
                SELECT 
                    attribute_id, 
                    attribute_type, 
                    COUNT(DISTINCT base_unit) AS unit_count
                FROM pbase
                GROUP BY 1, 2
            )
            SELECT 
                pbase.attribute_id,
                uc.unit_count,
                COUNT(DISTINCT pbase.normalised_measure) AS term_count
            FROM pbase
            LEFT JOIN uc USING(attribute_id)
            GROUP BY 1, 2
            ORDER BY term_count DESC
        """
        )

        # Identify which measure attributes to skip or keep
        condition = (measure_skip["term_count"] == 0) | (measure_skip["unit_count"] > 1)

        measure_ids_skip = measure_skip[condition]["attribute_id"].to_list()

        self.attribute_ids_size_measure = dict(
            zip(
                measure_skip[measure_skip["term_count"] > 0]["attribute_id"],
                measure_skip[measure_skip["term_count"] > 0]["term_count"],
            )
        )

        measure_id_str = ",".join(map(str, measure_ids_skip))
        query = f"""
            WITH cats AS (
                SELECT DISTINCT product_category_id
                FROM {CAT_ATTRIBUTE_TABLE}
            ),
            ub AS (
                SELECT 
                    mu.measure_id,
                    mu.unit
                FROM public.measure_unit mu
                WHERE mu.base
            ),
            attars as (
            select distinct attribute_id
            from  {CAT_ATTRIBUTE_TABLE}
            ),
            mtbase AS (
                SELECT
                    p.product_id,
                    cats.product_category_id,
                    pa.attribute_id,
                    a.name AS attribute,
                    a.type AS attribute_type,
                    CASE 
                        WHEN mu.base_multiplier IS NOT NULL THEN pa.measure * mu.base_multiplier 
                        ELSE pa.measure 
                    END AS normalised_measure,
                    COALESCE(ub.unit, mu.unit) AS base_unit
                FROM public.product_attribute pa
                LEFT JOIN public.product p USING(product_id)
                LEFT JOIN public.attribute a USING(attribute_id)
                JOIN cats USING(product_category_id)
                JOIN attars using (attribute_id)
                LEFT JOIN public.measure_unit mu USING(measure_unit_id)
                LEFT JOIN ub ON ub.measure_id = mu.measure_id
                WHERE pa.attribute_id NOT IN ({measure_id_str})
                AND a.type in ('measure', 'simple_measure')
                AND pa.measure IS NOT NULL
            )
            SELECT * FROM mtbase
        """
        self.save_query_to_sqlite(
            postgres_uri=self.database.uri,
            sqlite_path=self.sqlite_path,
            database_name="mtbase_data",
            query=query,
        )

        # Identify term attributes with no terms
        term_skip = self.database.query(
            f"""
            WITH attrs AS (
                SELECT cat.attribute_id
                FROM {CAT_ATTRIBUTE_TABLE} cat
                LEFT JOIN public.attribute at USING(attribute_id)
                WHERE at.type = 'term'
                GROUP BY 1
            )
            SELECT 
                attrs.attribute_id,
                COUNT(DISTINCT at.term) AS term_count
            FROM attrs
            LEFT JOIN attribute_term at USING(attribute_id)
            GROUP BY 1
            ORDER BY term_count DESC
        """
        )

        term_ids_skip = term_skip[term_skip["term_count"] == 0][
            "attribute_id"
        ].to_list()

        # Update attribute size mapping with valid terms
        valid_term_df = term_skip[term_skip["term_count"] > 0]
        self.attribute_ids_size_term = dict(
            dict(zip(valid_term_df["attribute_id"], valid_term_df["term_count"]))
        )

        # with open(self.attribute_ids_size, "w") as f:
        #     json.dump(attribute_ids_size, f)

        self._skip_id_list.extend(term_ids_skip)
        self._skip_id_list.extend(measure_ids_skip)
        self._skip_id_list = list(set(self._skip_id_list))  # Remove duplicates

    def _attribute_ids_by_pcat(self) -> None:
        sql = "SELECT product_category_id, attribute_id, pcat_attribute_order FROM report_data.attribute_order ORDER BY 1, 3"
        ao = self.database.query(sql)[["product_category_id", "attribute_id"]]
        attribute_ids_by_pcat = (
            ao.groupby("product_category_id")["attribute_id"]
            .apply(lambda x: list(x))
            .to_dict()
        )

        with open(self.attribute_ids_by_pcat_json, "w") as f:
            json.dump(attribute_ids_by_pcat, f)

    def _calculate_product_category_id_size(self) -> None:
        nof_products_id = {}
        pcat_ids = (
            self.database.query("select Distinct(product_category_id) from product")[
                "product_category_id"
            ]
            .dropna()
            .to_list()
        )
        for id in pcat_ids:
            count = self.database.query(
                f"select COUNT(product_id) from product where product_category_id = {id}"
            )["count"].to_list()[0]
            nof_products_id[int(id)] = count
        with open(self.pcat_id_size_json, "w") as f:
            json.dump(nof_products_id, f)

    def _create_vectordb(self) -> None:
        """Creating the vector database for attribute terms."""
        import time

        term_df = self.database.query(
            f"""WITH ate as (
        SELECT attribute_term_id, attribute_id, term, 'term' as type
        FROM attribute_term
    )
    SELECT ROW_NUMBER() OVER (PARTITION BY ate.attribute_id ORDER BY ate.term) - 1 AS term_order,
    ate.*
    FROM ate
    WHERE ate.attribute_id IN (SELECT DISTINCT attribute_id FROM {self.attributes_by_category_table})"""
        )
        terms = term_df["term"].values
        metadatas = term_df[["attribute_id", "term_order", "type"]].to_dict("records")
        docs = [Document(text=x[0], metadata=x[1]) for x in zip(terms, metadatas)]
        print("Inserting attribute terms into vector database...", end="")
        start = time.perf_counter()
        self.vdb.add_documents(docs=docs, split_text=False)
        end = time.perf_counter() - start
        print(f"done; Time taken: {end:.2f}s.")

    def _calculate_score_matrix_terms(self, attribute_id, batch_size):
        """Creating the score matrix of the given attribute ID.

        Args:
            attribute_id (int): Attribute ID.
            batch_size (int, optional): Batch size of calculating scores. Defaults to 1000.

        Returns:
            np.ndarray: A square matrix with the scores. Index of the terms can be found in the attribute_term_extended table.
        """
        from tqdm import tqdm

        docs = self.vdb.search_by_doc(attribute_id=attribute_id, ids_only=False)
        # create empty score matrix
        doc_ids = np.array(list(docs.keys()))
        size = doc_ids.shape[0]
        score_matrix = np.zeros(shape=(size, size))

        # create arrays for doc information
        doc_arr = np.array(list(docs.values()))
        term_orders = np.vectorize(lambda x: x.metadata["term_order"])(doc_arr)
        texts = np.vectorize(lambda x: x.text)(doc_arr)
        doc_orders = np.argsort(term_orders)
        texts = texts[doc_orders]
        doc_arr = doc_arr[doc_orders]
        doc_ids = doc_ids[doc_orders]
        term_orders = term_orders[doc_orders]

        # Gather the terms and their vectors

        # Calculating batches
        num_batch = (
            size // batch_size
            if (size // batch_size) == (size / batch_size)
            else (size // batch_size) + 1
        )
        batches = [
            (b * batch_size, min(size, (b + 1) * batch_size)) for b in range(num_batch)
        ]

        # doing batch search on vector database
        index_map = dict(zip(doc_ids, term_orders))
        print(f"Creating score matrix for attribute id {attribute_id}...")
        for batch in tqdm(batches):
            vectors = self.vdb.get_vectors_by_ids(doc_ids[batch[0] : batch[1]]).astype(
                dtype=np.float32
            )
            ids, scores = self.vdb._batch_search_by_vectors(
                vectors=vectors, scope_ids=doc_ids, top_k=size
            )
            del vectors
            # Get indexes of results in the score matrix
            get_index = np.vectorize(lambda x: index_map[x])
            indexes = get_index(ids)
            first_indexes = np.arange(batch[0], batch[1])
            first_indexes = np.tile(first_indexes.reshape(-1, 1), (1, scores.shape[1]))
            score_matrix[first_indexes, indexes] = scores
        score_matrix = (score_matrix + score_matrix.T) / 2
        return score_matrix

    def _calculate_score_matrix_measures(self, docs):

        # create arrays for doc information
        doc_arr = np.array(list(docs.values()))
        term_orders = np.vectorize(lambda x: x.metadata["term_order"])(doc_arr)
        doc_orders = np.argsort(term_orders)
        doc_arr = doc_arr[doc_orders]
        doc_arr_values = np.vectorize(lambda x: x["value"])(doc_arr)

        values_i, values_j = np.meshgrid(doc_arr_values, doc_arr_values, indexing="ij")
        both_nonzero = (values_i != 0) & (values_j != 0)
        both_zero = (values_i == 0) & (values_j == 0)
        one_zero = ~both_nonzero & ~both_zero
        score_matrix = np.zeros_like(values_i, dtype=float)

        ratio1 = values_i[both_nonzero] / values_j[both_nonzero]
        ratio2 = values_j[both_nonzero] / values_i[both_nonzero]
        score_matrix[both_nonzero] = np.minimum(ratio1, ratio2)

        score_matrix[both_zero] = 1.0
        score_matrix[one_zero] = 0.0

        return score_matrix

    def _create_score_matrices(self, batch_size: int = 1000) -> None:
        import os

        # Counts of terms by attribute ID
        os.makedirs(self.score_matrix_dir)
        mtbase_connections = self.create_local_engine(
            sqlite_path=self.sqlite_path, database_name="mtbase_data"
        )
        ate_df = pd.read_sql_query(
            sql="""
                SELECT attribute_id, normalised_measure
                FROM mtbase_view
                group by product_id, attribute_id, product_category_id
            """,
            con=mtbase_connections,
        )
        valid_ids_df = self.database.query(
            f"""
            SELECT DISTINCT attribute_id
            FROM {self.attributes_by_category_table}
        """
        )

        valid_ids = set(valid_ids_df["attribute_id"])

        measure_df = ate_df[ate_df["attribute_id"].isin(valid_ids)]
        measure_df = measure_df.sort_values(["attribute_id", "normalised_measure"])
        measure_df["term_order"] = measure_df.groupby("attribute_id").cumcount()

        print("Score matrix folder has been freshly created.")
        score_matrix_size = {}
        # Create the score matrix for term
        for attribute_id, size in self.attribute_ids_size_term:
            score_matrix = self._calculate_score_matrix_terms(
                attribute_id=attribute_id, batch_size=batch_size
            )
            with open(f"{self.score_matrix_dir}/{attribute_id}.pkl", "wb") as f:
                pickle.dump(score_matrix, f)
                score_matrix_size[int(attribute_id)] = int(size)
                del score_matrix

        for attribute_id, size in self.attribute_ids_size_measure:
            docs = {
                idx: {
                    "value": row["normalised_measure"],
                    "metadata": {
                        "attribute_id": row["attribute_id"],
                        "term_order": row["term_order"],
                        "type": "term",
                    },
                }
                for idx, row in measure_df[
                    measure_df["attribute_id"] == attribute_id
                ].iterrows()
            }
            score_matrix = self._calculate_score_matrix_measures(docs=docs)
            with open(f"{self.score_matrix_dir}/{attribute_id}.pkl", "wb") as f:
                pickle.dump(score_matrix, f)
                score_matrix_size[int(attribute_id)] = int(size)
                del score_matrix

        with open(f"{self.score_matrix_size_json}", "w") as f:
            json.dump(score_matrix_size, f)

    def _create_product_id_mapping(self, product_category_id: int) -> pd.Series:
        print(f"Creating mapping for product category id {product_category_id}...")
        mtbase_connection = self.create_local_engine(self.sqlite_path, "mtbase_data")
        order_measure = self.database.query(
            f"""
                SELECT DISTINCT attribute_id
                FROM {CAT_ATTRIBUTE_TABLE}
                WHERE product_category_id = {product_category_id}
                """
        )
        pg_attr_ids = [row for row in order_measure["attribute_id"]]
        attr_id_str = ", ".join(f"'{aid}'" for aid in pg_attr_ids)

        mapping_measure_query = f"""
            WITH measure_ordering AS (
            SELECT
                m.attribute_id,
                m.normalised_measure,
                ROW_NUMBER() OVER (
                    PARTITION BY m.attribute_id
                    ORDER BY m.normalised_measure
                ) - 1 AS term_order
            FROM mtbase_data m
            WHERE m.product_category_id = {product_category_id}
            AND m.attribute_id IN ({attr_id_str})
        ),
        measure_max_sizes AS (
            SELECT
                attribute_id,
                MAX(term_order) + 1 AS num_terms
            FROM measure_ordering
            GROUP BY attribute_id
        )
        SELECT
            m.*,
            mo.term_order,
            ms.num_terms,
            'measure' AS value_type
        FROM mtbase_data m
        LEFT JOIN measure_ordering mo
            ON m.attribute_id = mo.attribute_id AND m.normalised_measure = mo.normalised_measure
        LEFT JOIN measure_max_sizes ms ON m.attribute_id = ms.attribute_id
        WHERE m.product_category_id = {product_category_id}
        AND m.attribute_id IN ({attr_id_str})
        ORDER BY m.product_id, m.product_category_id;
"""
        df_measure = pd.read_sql_query(mapping_measure_query, mtbase_connection)
        df_measure["term_order_final"] = df_measure["term_order"].fillna(
            df_measure["num_terms"]
        )
        df_measure.drop_duplicates(
            ["attribute_id", "product_id"], keep="first", inplace=True
        )
        ptm_measure = df_measure.groupby(by="product_id")["term_order_final"].apply(
            lambda x: np.array(x)
        )

        df_term = self.database.query(
            """
        WITH relevant_term_attributes AS (
            SELECT DISTINCT attribute_id
            FROM attribute_order_test_v1
        ),
        term_attributes AS (
            SELECT
                p.product_id,
                p.product_category_id,
                ao.attribute_id,
                a.type,
                ao.pcat_attribute_order,
                pa.product_attribute_id,
                at.term,
                'term' AS value_type
            FROM public.product p
            LEFT JOIN report_data.attribute_order ao USING(product_category_id)
            LEFT JOIN public.product_attribute pa ON (p.product_id = pa.product_id AND ao.attribute_id = pa.attribute_id)
            LEFT JOIN public.attribute a ON (ao.attribute_id = a.attribute_id)
            LEFT JOIN public.attribute_term at ON (pa.attribute_term_id = at.attribute_term_id)
            WHERE p.product_category_id = 385
            AND a.type = 'term'
            ORDER BY p.product_id, p.product_category_id, ao.pcat_attribute_order
        ),
        term_ordering AS (
            SELECT
                at.attribute_term_id,
                at.attribute_id,
                at.term,
                ROW_NUMBER() OVER (PARTITION BY at.attribute_id ORDER BY at.term) - 1 AS term_order
            FROM attribute_term at
            INNER JOIN relevant_term_attributes ra ON at.attribute_id = ra.attribute_id
        ),
        term_max_sizes AS (
            SELECT
                attribute_id,
                MAX(term_order) + 1 AS num_terms
            FROM term_ordering
            GROUP BY attribute_id
        )
        SELECT
            ta.*,
            tor.term_order,
            tms.num_terms
        FROM term_attributes ta
        LEFT JOIN term_ordering tor ON (ta.term = tor.term AND ta.attribute_id = tor.attribute_id)
        LEFT JOIN term_max_sizes tms ON (ta.attribute_id = tms.attribute_id)
        ORDER BY ta.product_id, ta.product_category_id, ta.pcat_attribute_order;

        """
        )
        df_term["term_order_final"] = df_term["term_order"].fillna(df_term["num_terms"])
        df_term.drop_duplicates(
            ["attribute_id", "product_id"], keep="first", inplace=True
        )
        ptm_term = df_term.groupby(by="product_id")["term_order_final"].apply(
            lambda x: np.array(x)
        )
        all_product_ids = ptm_measure.index.union(ptm_term.index)
        combined_ptm = pd.Series(
            {
                pid: np.concatenate([ptm_term[pid], ptm_measure[pid]])
                for pid in all_product_ids
            }
        )
        return combined_ptm

    def _compute_product_mapping(self):
        from threading import Thread
        import os

        self._create_score_matrices()
        with open(f"{self.score_matrix_size_json}", "r") as f:
            size = json.load(f)
            self.attribute_sizes = {int(k): v for k, v in size.items()}
            del size
        pcategory_id = self.database.query(
            f"""SELECT product_category_id
                FROM report_data.attribute_order
                GROUP BY product_category_id;"""
        )["product_category_id"].to_list()
        os.makedirs(self.product_mapping_dir)
        print("Score product mapping has been freshly created.")
        for pcat_id in pcategory_id:
            mapped_products = self._create_product_id_mapping(pcat_id)
            if not mapped_products.empty:
                raw_term_indices = np.array(
                    mapped_products.values.tolist(), dtype=np.int32
                )
                product_ids = mapped_products.index.values
                indices_pid = (raw_term_indices, product_ids)
                # Saving the product mapping to a pkl file
                trds = [
                    Thread(
                        target=save_product_mapping,
                        args=(pcat_id, indices_pid, self.product_mapping_dir),
                    )
                ]
                [trd.start() for trd in trds]
                [trd.join() for trd in trds]
                del mapped_products, product_ids, indices_pid
                gc.collect()

    def _copy_tables_for_inference(self):
        """For the inference we need to copy the attribute_order and product tables"""
        pass

    def run_preprocessing(self):
        self.attributes_by_category_table
        self._create_vectordb()
        self._compute_product_mapping()


if __name__ == "__main__":
    from contextai.Embeddings.api_embeddings import APIEmbeddings
    from contextdb.Engine import PostgresEngine

    embeddings = APIEmbeddings(base_url="http://127.0.0.1:5004")
    database = PostgresEngine(
        host="hulk.contextworld.com",
        user="machine_learning_l2_user",
        password="RSx977LQ",
        port="6432",
        database="gp_mm_live_20241117",
    )
    test = Preprocessor(
        database=database,
        embeddings=embeddings,
        base_dir="/mnt/data/machine_learning/pss",
    )
    test.run()
