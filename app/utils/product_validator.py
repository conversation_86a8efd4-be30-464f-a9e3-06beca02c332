from app.schemas.schemas import ValidationResult
import json
import os
import pickle
import numpy as np
import time
from functools import lru_cache
from typing import Tuple, Dict
from numba import jit


@jit(nopython=True, cache=True)
def _check_same_category_numba(categories: np.ndarray) -> bool:
    """Check if all categories are the same using numba."""
    if len(categories) == 0:
        return True
    first_cat = categories[0]
    for i in range(1, len(categories)):
        if categories[i] != first_cat:
            return False
    return True


@jit(nopython=True, cache=True)
def _validate_attribute_values_numba(
    product_attr_values: np.ndarray, attr_sizes: np.ndarray
) -> Tuple[bool, np.ndarray, np.ndarray]:
    """Validate attribute values against sizes using numba."""
    n_products, n_attrs = product_attr_values.shape
    failing_products = []
    failing_attrs = []

    for i in range(n_products):
        for j in range(n_attrs):
            if product_attr_values[i, j] >= attr_sizes[j]:
                failing_products.append(i)
                failing_attrs.append(j)

    return (
        len(failing_products) == 0,
        np.array(failing_products),
        np.array(failing_attrs),
    )


class DataCache:
    """Singleton cache for database files to avoid repeated loading."""

    _instance = None
    _cache = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @lru_cache(maxsize=32)
    def load_pickle(self, filepath: str) -> any:
        """Load and cache pickle files."""
        with open(filepath, "rb") as f:
            return pickle.load(f)

    @lru_cache(maxsize=32)
    def load_json(self, filepath: str) -> dict:
        """Load and cache JSON files."""
        with open(filepath, "r") as f:
            return json.load(f)

    def clear_cache(self):
        """Clear the cache."""
        self.load_pickle.cache_clear()
        self.load_json.cache_clear()


class ProductQueryValidator:
    """
    A class for validating product query parameters.

    Errors raised:
        - ValueError: If query product IDs are not in the same category.
        - ValueError: If one or more query product IDs do not exist.
        - ValueError: If any query product has no attributes matching the provided weight keys.
        - ValueError: If any attribute in the weight dictionary does not exist for the specified product category.
        - ValueError: If no scope product IDs are found in the database.
        - ValueError: If scope product IDs are not in the same category as the query product IDs.

    Warnings issued:
        - UserWarning: Some scope product IDs are not found in the database.
        - UserWarning: Requested top K results exceeds the number of available scope product IDs.


    Attributes:
        data (dict): Input data containing query parameters
        result (ValidationResult): Object to store validation errors and warnings
        query_ids (List[int]): List of product IDs to query
        scope_ids (List[int]): Optional list of product IDs to scope the search
        weights (Dict[str, float]): Optional attribute weights for similarity calculation
        top_k (Optional[int]): Number of results to return
        category_id (Optional[int]): Category ID of the query products
        product_ids (List[int]): List of all product IDs in the database
        product_category_id (List[int]):
        attribute_id_by_category (Dict[str, List[int]]): Mapping of categories to valid attribute IDs
        attribute_id_sizes (Dict[str, int]): Mapping of attribute IDs to their sizes
        product_mapping (Tuple[np.ndarray, np.ndarray]): Product attribute values and product IDs
    """

    def __init__(self, data: dict, db: str):
        """
        Initialize the validator with query data and database path.

        Args:
            data (dict): Input data containing query parameters
            db (str): Path to the database directory
        """
        self.data = data
        self.result = ValidationResult()
        self.query_ids = np.array(data["query_ids"], dtype=np.int64)
        self.scope_ids = np.array(data.get("scope_ids", []), dtype=np.int64)
        self.weights = data.get("weights", {})
        self.top_k = data.get("top_k")
        self.category_id = None
        self.db = db

        # Initialize timing dictionary to track method execution times
        self.timing: Dict[str, float] = {}

        # Use cached data loading
        self._cache = DataCache()

        # Load necessary data from database using cache
        self.product_ids = self._cache.load_pickle(os.path.join(db, "products.pkl"))
        self.product_category_id = self._cache.load_pickle(
            os.path.join(db, "product_category_id.pkl")
        )
        self.attribute_id_by_category = self._cache.load_json(
            os.path.join(db, "attribute_ids_by_pcat.json")
        )
        self.attribute_id_sizes = self._cache.load_json(
            os.path.join(db, "attribute_id_size.json")
        )

        # Pre-compute lookup structures for faster access
        self._product_id_to_index = {
            pid: idx for idx, pid in enumerate(self.product_ids)
        }
        self.product_mapping = None

    def _time_method(self, method_name: str, method_func, *args, **kwargs):
        """
        Helper method to time the execution of validation methods.

        Args:
            method_name (str): Name of the method being timed
            method_func: The method to execute
            *args: Arguments to pass to the method
            **kwargs: Keyword arguments to pass to the method

        Returns:
            The result of the method execution
        """
        start_time = time.time()
        result = method_func(*args, **kwargs)
        end_time = time.time()
        self.timing[method_name] = end_time - start_time
        return result

    def validate(self) -> ValidationResult:
        """
        Validate product query parameters using optimized NumPy operations.

        Returns:
            ValidationResult: Object containing validation errors and warnings
        """
        # Check if query IDs exist and are in the same category
        if not self._time_method("validate_product_ids", self._validate_product_ids):
            return self.result

        # Set category ID based on first query product
        self._time_method("set_category_id", self._set_category_id)

        # Check if weights are valid for the category
        if not self._time_method(
            "check_weights_valid_for_category", self._check_weights_valid_for_category
        ):
            return self.result

        # Check if query IDs have attributes specified in weights
        if not self._time_method(
            "check_query_ids_have_attrs", self._check_query_ids_have_attrs
        ):
            return self.result

        # Check scope IDs if provided
        if len(self.scope_ids) > 0:
            if not self._time_method("check_scope_ids", self._check_scope_ids):
                return self.result

        # Run warning checks
        self._time_method("run_warnings", self._run_warnings)

        return self.result

    def has_error(self) -> bool:
        """
        Check if validation has encountered any errors.

        Returns:
            bool: True if there are errors, False otherwise
        """
        return bool(self.result.errors)

    def get_timing_info(self) -> Dict[str, float]:
        """
        Get timing information for each validation method.

        Returns:
            Dict[str, float]: Dictionary mapping method names to execution times in seconds
        """
        return self.timing.copy()

    def print_timing_info(self) -> None:
        """
        Print timing information for each validation method in a formatted way.
        """
        if not self.timing:
            print("No timing information available.")
            return

        print("\nValidation Method Timing:")
        print("-" * 50)
        total_time = 0
        for method_name, execution_time in self.timing.items():
            print(f"{method_name:<35}: {execution_time*1000:>8.3f} ms")
            total_time += execution_time
        print("-" * 50)
        print(f"{'Total validation time':<35}: {total_time*1000:>8.3f} ms")

    def _set_category_id(self) -> None:
        """Set the category ID based on the first query product."""
        # Use pre-computed lookup for faster access
        first_query_id = self.query_ids[0]
        if first_query_id in self._product_id_to_index:
            query_index = self._product_id_to_index[first_query_id]
            self.category_id = self.product_category_id[query_index]

            # Load product mapping using cache
            mapping_path = os.path.join(
                self.db, f"product_mapping/{int(self.category_id)}.pkl"
            )
            self.product_mapping = self._cache.load_pickle(mapping_path)

    def _validate_product_ids(self) -> bool:
        """
        Validate that query product IDs exist and belong to the same category.

        Returns:
            bool: True if validation passes, False otherwise
        """
        # Use pre-computed lookup for faster access
        missing_ids = []
        query_indices = []

        for qid in self.query_ids:
            if qid in self._product_id_to_index:
                query_indices.append(self._product_id_to_index[qid])
            else:
                missing_ids.append(qid)

        if missing_ids:
            self.result.add_error(f"Query product IDs not exist: {missing_ids}")
            return False

        # Convert to numpy array for vectorized operations
        query_indices = np.array(query_indices, dtype=np.int64)

        # Get the categories and check if all are the same using numba
        categories = self.product_category_id[query_indices]
        if not _check_same_category_numba(categories):
            category_map = dict(zip(self.query_ids.tolist(), categories.tolist()))
            self.result.add_error(
                f"Query product IDs not in same category: {category_map}"
            )
            return False

        return True

    def _check_weights_valid_for_category(self) -> bool:
        """
        Check if weights reference valid attributes for the product category.

        Returns:
            bool: True if validation passes, False otherwise
        """
        if not self.weights:
            return True

        # Get valid attributes for the category
        valid_attrs = np.array(
            self.attribute_id_by_category.get(str(int(self.category_id)), []),
            dtype=np.int64,
        )

        # Convert weight keys to integers and create a NumPy array
        weight_attrs = np.array(
            [int(attr) for attr in self.weights.keys()], dtype=np.int64
        )

        # Find invalid attributes
        invalid_attrs = weight_attrs[~np.isin(weight_attrs, valid_attrs)]

        if len(invalid_attrs) > 0:
            self.result.add_error(
                f"Attributes in weights not exist for category {self.category_id}: {invalid_attrs.tolist()}"
            )
            return False

        return True

    def _check_query_ids_have_attrs(self) -> bool:
        """
        Check if query products have values for the specified attributes.

        Returns:
            bool: True if validation passes, False otherwise
        """
        if not self.weights:
            return True

        # Get valid attributes for the category
        start_time = time.time()
        valid_attrs = np.array(
            self.attribute_id_by_category.get(str(int(self.category_id)), []),
            dtype=np.int64,
        )

        # Convert weight keys to integers and create a NumPy array
        weight_attrs = np.array(
            [int(attr) for attr in self.weights.keys()], dtype=np.int64
        )
        self.timing["check_attrs_setup"] = time.time() - start_time

        # Find indices of query products in the product mapping using vectorized operations
        start_time = time.time()
        product_indices = np.where(np.isin(self.product_mapping[1], self.query_ids))[0]

        # Find indices of weight attributes in the valid attributes list
        attr_indices = np.array(
            [np.where(valid_attrs == attr)[0][0] for attr in weight_attrs]
        )

        # Get attribute sizes for all weight attributes
        attr_sizes = np.array(
            [self.attribute_id_sizes.get(str(attr), 0) for attr in weight_attrs],
            dtype=np.int64,
        )

        # Extract product attribute values for all query products and all weight attributes
        product_attr_values = self.product_mapping[0][product_indices][:, attr_indices]
        self.timing["check_attrs_data_extraction"] = time.time() - start_time

        # Use numba-optimized validation
        start_time = time.time()
        is_valid, failing_products, failing_attrs = _validate_attribute_values_numba(
            product_attr_values, attr_sizes
        )
        self.timing["check_attrs_numba_validation"] = time.time() - start_time

        if not is_valid:
            # Build error details
            start_time = time.time()
            failing_details = []
            for i in range(len(failing_products)):
                prod_idx = failing_products[i]
                attr_idx = failing_attrs[i]
                attr_id = weight_attrs[attr_idx]
                product_id = self.query_ids[prod_idx]
                attr_size = attr_sizes[attr_idx]
                product_value = product_attr_values[prod_idx, attr_idx]
                failing_details.append(
                    {
                        "attribute_id": int(attr_id),
                        "product_id": int(product_id),
                        "attribute_size": int(attr_size),
                        "product_value": int(product_value),
                    }
                )
            self.timing["check_attrs_error_details"] = time.time() - start_time

            self.result.add_error(
                f"Query product IDs have no values for given attributes: {failing_details}"
            )
            return False

        return True

    def _check_scope_ids(self) -> bool:
        """
        Check if scope IDs exist and belong to the same category as query IDs.

        Returns:
            bool: True if validation passes, False otherwise
        """
        if len(self.scope_ids) == 0:
            return True

        # Use pre-computed lookup for faster access
        existing_indices = []
        valid_scope_ids = []
        scope_indices = []

        for i, sid in enumerate(self.scope_ids):
            if sid in self._product_id_to_index:
                existing_indices.append(i)
                valid_scope_ids.append(sid)
                scope_indices.append(self._product_id_to_index[sid])

        # Check if any scope IDs exist
        if not existing_indices:
            self.result.add_error(
                f"No product IDs in scope exist in database: {self.scope_ids.tolist()}"
            )
            return False

        # Convert to numpy arrays for vectorized operations
        scope_indices = np.array(scope_indices, dtype=np.int64)
        scope_categories = self.product_category_id[scope_indices]

        # Check if scope products are in the same category as query products using vectorization
        different_mask = scope_categories != self.category_id
        if np.any(different_mask):
            different_indices = np.where(different_mask)[0]
            different_category_ids = []
            for idx in different_indices:
                different_category_ids.append(
                    {
                        "scope_id": int(valid_scope_ids[idx]),
                        "scope_category": int(scope_categories[idx]),
                        "query_category": int(self.category_id),
                    }
                )

            self.result.add_error(
                f"Product IDs in scope not in the same category as queries: {different_category_ids}"
            )
            return False

        return True

    def _run_warnings(self) -> None:
        """Generate warnings for non-critical issues."""
        if len(self.scope_ids) > 0:
            self._warn_scope_ids_not_in_db()
        self._warn_topk_larger_than_scope()

    def _warn_scope_ids_not_in_db(self) -> None:
        """Warn if some scope IDs don't exist in the database."""
        # Use pre-computed lookup for faster checking
        missing = []
        for sid in self.scope_ids:
            if sid not in self._product_id_to_index:
                missing.append(sid)

        if missing:
            self.result.add_warning(
                f"Some provided scope product IDs not in database: {missing}"
            )

    def _warn_topk_larger_than_scope(self) -> None:
        """Warn if top_k is larger than the number of scope IDs."""
        if self.top_k and len(self.scope_ids) < self.top_k:
            self.result.add_warning(
                f"Top K ({self.top_k}) larger than the size of scope IDs ({len(self.scope_ids)})"
            )


def test_validator():
    """
    Test the ProductQueryValidator with sample data.

    This function demonstrates how to use the ProductQueryValidator class
    with a predefined set of test data.

    Returns:
        ValidationResult: The validation result object
    """
    import json

    test_data = {
        "query_ids": [4689084, 4773954, 4349718],
        "scope_ids": [
            2303436,
            2307419,
            2310419,
            2312933,
            2315924,
            2316128,
            2317400,
            2317460,
            2318220,
            2318314,
            2338521,
            2338523,
            2347896,
            2347954,
            2348079,
            2348821,
            2348846,
            2350010,
            2350356,
            2352721,
        ],
        "top_k": 5,
    }
    with open("app/services/config.json", "r") as f:
        config = json.load(f)
    db_path = config["db_path"]
    validator = ProductQueryValidator(test_data, db_path)

    result = validator.validate()

    # Display validation results
    if result.errors:
        print("Validation errors:")
        for error in result.errors:
            print(f"- {error}")

    if result.warnings:
        print("Validation warnings:")
        for warning in result.warnings:
            print(f"- {warning}")

    if not result.errors and not result.warnings:
        print("Validation passed successfully!")

    # Display timing information
    validator.print_timing_info()

    print(len(test_data["scope_ids"]))

    return result


if __name__ == "__main__":
    test_result = test_validator()
    print(f"Has errors: {bool(test_result.errors)}")
