import asyncio
import os
import json
import time
from typing import Dict, Optional, Set
from dataclasses import dataclass
from app.core.request_processor import RequestProcessor
from app.utils.logging import setup_logging

logger = setup_logging()

from dotenv import load_dotenv
load_dotenv()

BASE_DIRECTORY = os.getenv("BASE_DIRECTORY")
PROCESSOR_IDLE_TIMEOUT = float(os.getenv("PROCESSOR_IDLE_TIMEOUT", "1800"))  # 30 minutes default
CLEANER_INTERVAL_SECONDS = float(os.getenv("PROCESSOR_CLEANER_INTERVAL", "60"))  # 1 minute default


@dataclass
class ProcessorEntry:
    processor: RequestProcessor
    last_access_ts: float
    created_at: float


class InferenceRegistry:
    """Singleton registry that manages RequestProcessor instances with lazy loading and idle cleanup."""
    
    _instance: Optional['InferenceRegistry'] = None
    _lock = asyncio.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        
        self.processors: Dict[str, ProcessorEntry] = {}
        self.registry_lock = asyncio.Lock()
        self.cleaner_task: Optional[asyncio.Task] = None
        self._initialized = True
        
        logger.info("registry.initialized")

    def _discover_existing_databases(self) -> Set[str]:
        """Return the set of database folder names currently present on disk."""
        try:
            # Prefer databases.json if present
            dbs_json = os.path.join(BASE_DIRECTORY, "databases.json")
            if os.path.exists(dbs_json):
                with open(dbs_json, "r") as f:
                    data = json.load(f)
                if isinstance(data, dict) and "databases" in data:
                    return set(map(str, data["databases"]))
                if isinstance(data, list):
                    return set(map(str, data))
        except Exception as e:
            logger.warning("registry.discover_fallback", error=str(e))

        # Fallback: list directories under BASE_DIRECTORY
        try:
            entries = os.listdir(BASE_DIRECTORY or ".")
            return set(
                name
                for name in entries
                if os.path.isdir(os.path.join(BASE_DIRECTORY, name))
                and not name.startswith(".")
            )
        except Exception as e:
            logger.error("registry.discover_failed", error=str(e))
            return set()

    def _resolve_default_database(self) -> str:
        """Resolve default database from info.json when database param is empty."""
        try:
            with open(os.path.join(BASE_DIRECTORY, "info.json"), "r") as f:
                info = json.load(f)
            return info.get("database", "")
        except Exception as e:
            logger.error("registry.default_db_resolution_failed", error=str(e))
            raise ValueError("Cannot resolve default database and no database specified")

    async def get_or_create(self, database: str) -> RequestProcessor:
        """
        Get existing processor or create new one (lazy loading).
        Updates last_access_ts on each call.
        """
        async with self.registry_lock:
            # Resolve default database if empty
            if not database or database == "":
                database = self._resolve_default_database()
            
            # Validate database folder exists
            db_path = os.path.join(BASE_DIRECTORY, database)
            if not os.path.exists(db_path):
                logger.error("registry.database_not_found", database=database, path=db_path)
                raise ValueError(f"Database folder not found: {database}")
            
            current_time = time.time()
            
            # Return existing processor if present
            if database in self.processors:
                entry = self.processors[database]
                entry.last_access_ts = current_time
                logger.debug("registry.processor_reused", database=database)
                return entry.processor
            
            # Create new processor (lazy loading)
            logger.info("registry.processor_creating", database=database)
            try:
                processor = RequestProcessor(database_str=database)
                await processor.start()
                
                entry = ProcessorEntry(
                    processor=processor,
                    last_access_ts=current_time,
                    created_at=current_time
                )
                self.processors[database] = entry
                
                logger.info("registry.processor_created", database=database)
                return processor
                
            except Exception as e:
                logger.error("registry.processor_creation_failed", database=database, error=str(e))
                raise

    async def remove(self, database: str, reason: str = "manual") -> bool:
        """Remove and stop a processor. Returns True if processor was removed."""
        async with self.registry_lock:
            if database not in self.processors:
                return False
            
            entry = self.processors[database]
            try:
                await entry.processor.stop()
            except Exception as e:
                logger.warning("registry.processor_stop_failed", database=database, error=str(e))
            
            del self.processors[database]
            logger.info("registry.processor_removed", database=database, reason=reason)
            return True

    async def _cleaner_loop(self):
        """Background task that handles both folder-based and idle-based cleanup."""
        while True:
            try:
                current_time = time.time()
                existing_databases = self._discover_existing_databases()
                
                # Collect databases to remove
                to_remove = []
                
                async with self.registry_lock:
                    for database, entry in self.processors.items():
                        db_path = os.path.join(BASE_DIRECTORY, database)
                        
                        # Check if database folder still exists
                        if (database not in existing_databases) or (not os.path.exists(db_path)):
                            to_remove.append((database, "folder_missing"))
                            continue
                        
                        # Check if processor is idle
                        idle_time = current_time - entry.last_access_ts
                        if idle_time > PROCESSOR_IDLE_TIMEOUT:
                            to_remove.append((database, f"idle_{idle_time:.0f}s"))
                
                # Remove processors outside the lock to avoid deadlock
                for database, reason in to_remove:
                    await self.remove(database, reason)
                
                if to_remove:
                    logger.info("registry.cleanup_completed", removed_count=len(to_remove))
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("registry.cleaner_error", error=str(e))
            
            await asyncio.sleep(CLEANER_INTERVAL_SECONDS)

    async def start(self):
        """Start the registry and its background cleaner."""
        if self.cleaner_task is None or self.cleaner_task.done():
            self.cleaner_task = asyncio.create_task(self._cleaner_loop())
            logger.info("registry.started", 
                       idle_timeout=PROCESSOR_IDLE_TIMEOUT,
                       cleaner_interval=CLEANER_INTERVAL_SECONDS)

    async def stop(self):
        """Stop the registry and all processors."""
        # Stop cleaner
        if self.cleaner_task and not self.cleaner_task.done():
            self.cleaner_task.cancel()
            try:
                await self.cleaner_task
            except asyncio.CancelledError:
                pass
        
        # Stop all processors
        async with self.registry_lock:
            for database, entry in list(self.processors.items()):
                try:
                    await entry.processor.stop()
                except Exception as e:
                    logger.warning("registry.processor_stop_failed", database=database, error=str(e))
            
            self.processors.clear()
        
        logger.info("registry.stopped")

    def get_stats(self) -> Dict:
        """Get registry statistics for monitoring."""
        current_time = time.time()
        stats = {
            "total_processors": len(self.processors),
            "processors": {}
        }
        
        for database, entry in self.processors.items():
            idle_time = current_time - entry.last_access_ts
            stats["processors"][database] = {
                "idle_seconds": idle_time,
                "created_at": entry.created_at,
                "queue_size": entry.processor.request_queue.qsize(),
                "active_requests": len(entry.processor.active_requests)
            }
        
        return stats


# Global registry instance
registry = InferenceRegistry()
