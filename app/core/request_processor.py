import asyncio
import psutil
import time
import json
import os
from typing import Optional, Dict, List
from datetime import datetime
from fastapi import HTT<PERSON>Exception
from app.core.inference import InferenceProductSearch
from app.utils.logging import setup_logging

logger = setup_logging()

from dotenv import load_dotenv
load_dotenv()
BASE_DIRECTORY = os.getenv("BASE_DIRECTORY")


class MemoryConfig:
    TOTAL_RAM = 50 * 1024 * 1024 * 1024  # 50GB in bytes
    MIN_FREE_MEMORY = 2 * 1024 * 1024 * 1024
    QUEUE_CHECK_INTERVAL = 0.1  # seconds
    MAX_QUEUE_SIZE = 100
    MAX_WAIT_TIME = 60 * 60  # 1 hour in seconds


class QueuedRequest:
    def __init__(self, func, args, kwargs):
        self.func = func
        self.args = args
        self.kwargs = kwargs
        self.future = asyncio.Future()
        self.timestamp = datetime.now()


class RequestProcessor:
    def __init__(self, database_str: str):
        self.pg = database_str
        self.inference = InferenceProductSearch(
            database=database_str,
        )
        self.request_queue = asyncio.Queue(maxsize=MemoryConfig.MAX_QUEUE_SIZE)
        self.background_task = None
        self.active_requests = {}
        self.last_access_time = time.time()  # Track last access for idle cleanup
        
        with open(
            os.path.join(BASE_DIRECTORY, database_str, "pcat_id_size.json"), "r"
        ) as f:
            self.product_category_id_size = json.load(f)
        self.process = psutil.Process()

    def get_current_memory_usage(self) -> float:
        """Get current memory usage in bytes"""
        return self.process.memory_info().rss

    def get_available_memory(self) -> float:
        """Get available system memory in bytes"""
        return MemoryConfig.TOTAL_RAM - self.get_current_memory_usage()

    def estimate_request_memory(
        self,
        category: int,
        weights: Optional[Dict[str, float]],
        scope_product_id: Optional[List[int]],
    ) -> float:
        """
        Estimate memory requirement for a request based on input parameters
        Returns estimated memory in bytes
        """
        # Base memory requirement (30MB)
        mb_scale = 1024**2
        base_memory = 30 * mb_scale

        if scope_product_id:
            products_memory = len(scope_product_id) / 100 * mb_scale
        else:
            products_memory = (
                self.product_category_id_size[str(category)] / 100
            ) * mb_scale

        if weights:
            weights_memory = len(weights) * 5 * mb_scale
        else:
            weights_memory = (
                self.product_category_id_size[str(category)] / 100
            ) * mb_scale

        total_memory = base_memory + products_memory + weights_memory

        return total_memory

    async def can_accept_request(self, estimated_memory: float) -> bool:
        """Check if we can accept a new request based on current memory usage"""
        available_memory = self.get_available_memory()
        active_requests_memory = sum(
            req.get("estimated_memory", 0) for req in self.active_requests.values()
        )

        required_memory = estimated_memory
        remaining_memory = available_memory - active_requests_memory - required_memory

        logger.info(remaining_memory=remaining_memory / 1024**3)

        return remaining_memory >= MemoryConfig.MIN_FREE_MEMORY

    async def start(self):
        """Start the queue processor if not already running"""
        if self.background_task is None or self.background_task.done():
            self.background_task = asyncio.create_task(self._process_queue())
            logger.info("queue_processor.started", database=self.pg)

    async def stop(self):
        """Gracefully stop the queue processor"""
        if self.background_task and not self.background_task.done():
            self.background_task.cancel()
            try:
                await self.background_task
            except asyncio.CancelledError:
                pass
            logger.info("queue_processor.stopped", database=self.pg)

    async def _process_queue(self):
        """Background task to process queued requests"""
        while True:
            try:
                if not self.request_queue.empty():
                    queued_request = await self.request_queue.get()

                    # Check for timeout
                    if (
                        datetime.now() - queued_request.timestamp
                    ).seconds > MemoryConfig.MAX_WAIT_TIME:
                        if not queued_request.future.done():
                            queued_request.future.set_exception(
                                HTTPException(
                                    status_code=408,
                                    detail="Request timeout while waiting in queue",
                                )
                            )
                        logger.warning(
                            "request.timed_out", request_id=id(queued_request)
                        )
                        self.request_queue.task_done()
                        continue

                    # Check memory availability
                    estimated_memory = self.estimate_request_memory(
                        queued_request.args[1],
                        queued_request.args[2],
                        queued_request.args[-1],
                    )

                    if not await self.can_accept_request(estimated_memory):
                        # If can't process now, put back in queue
                        await self.request_queue.put(queued_request)
                        await asyncio.sleep(MemoryConfig.QUEUE_CHECK_INTERVAL)
                        continue

                    logger.info(
                        "request.processing.start", request_id=id(queued_request)
                    )
                    asyncio.create_task(
                        self._process_single_request(queued_request, estimated_memory)
                    )

                await asyncio.sleep(MemoryConfig.QUEUE_CHECK_INTERVAL)

            except Exception as e:
                logger.error("queue_processor.error", error=str(e))

    async def _process_single_request(self, queued_request, estimated_memory: float):
        """Process a single queued request"""
        request_id = str(id(queued_request))
        log = logger.bind(request_id=request_id, func_name=queued_request.func.__name__)

        self.active_requests[request_id] = {
            "start_time": time.time(),
            "estimated_memory": estimated_memory,
        }

        try:
            log.info("request.processing.started")
            start_time = time.time()

            memory_before = self.get_current_memory_usage()
            result = await queued_request.func(
                *queued_request.args, **queued_request.kwargs
            )
            memory_after = self.get_current_memory_usage()

            actual_memory = max(0, memory_after - memory_before)
            process_time = time.time() - start_time

            log.info(
                "request.processing.completed",
                process_time=f"{process_time:.4f}s",
                estimated_memory=estimated_memory,
                actual_memory=actual_memory,
                result_size=len(result) if result else 0,
            )

            if not queued_request.future.done():
                queued_request.future.set_result(result)

        except Exception as e:
            log.error(
                "request.processing.failed",
                error=str(e),
                process_time=f"{time.time() - start_time:.4f}s",
            )
            if not queued_request.future.done():
                queued_request.future.set_exception(e)
        finally:
            if request_id in self.active_requests:
                del self.active_requests[request_id]
            log.info("request.task_done")
            self.request_queue.task_done()

    async def queue_request(self, func, *args, **kwargs):
        """Add request to queue and wait for result"""
        # Update last access time for idle tracking
        self.last_access_time = time.time()
        
        queued_request = QueuedRequest(func, args, kwargs)
        request_id = id(queued_request)

        log = logger.bind(request_id=request_id, func_name=func.__name__, database=self.pg)
        log.info("request.queued", args=args, kwargs=kwargs)

        try:
            await self.request_queue.put(queued_request)
            log.info("request.added_to_queue", queue_size=self.request_queue.qsize())

            result = await asyncio.wait_for(
                queued_request.future, timeout=MemoryConfig.MAX_WAIT_TIME
            )
            log.info("request.completed")
            return result

        except asyncio.TimeoutError:
            log.error("request.timeout")
            raise HTTPException(
                status_code=408, detail="Request timeout while waiting in queue"
            )
        except asyncio.QueueFull:
            log.error("request.queue_full")
            raise HTTPException(
                status_code=503,
                detail="Service temporarily unavailable. Queue is full.",
            )
