# app/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.endpoints import products
from app.core.processor_registry import registry

import os
from dotenv import load_dotenv

load_dotenv()

DATABASE = os.getenv("DATABASE")


class ProductLineup:
    def __init__(self):
        self.app = FastAPI()
        self.setup_routers()
        self.setup_cors()
        self.setup_lifecycle()

    def setup_routers(self):
        self.app.include_router(products.router)

    def setup_cors(self):
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            allow_headers=["*"],
        )

    def setup_lifecycle(self):
        @self.app.on_event("startup")
        async def on_startup():
            await registry.start()
            # Optional: warm the default database to load files once and be ready
            if DATABASE:
                try:
                    await registry.get_or_create(DATABASE)
                except Exception:
                    # If warmup fails, continue to allow runtime resolution
                    pass

        @self.app.on_event("shutdown")
        async def on_shutdown():
            await registry.stop()


def create_app():
    product_lineup = ProductLineup()
    return product_lineup.app


# Create the app instance for ASGI servers
app = create_app()


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        ssl_keyfile="/app/pss_storage/ssl/ssl.key",
        ssl_certfile="/app/pss_storage/ssl/cert.pem",
        ssl_keyfile_password="mlteamssl",
        reload=True,
    )
