from fastapi import APIRouter, Depends, HTTPException, Body
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from app.utils.logging import setup_logging

# Removed unused db_utils imports since we're using ProductQueryValidator
from app.core.processor_registry import registry
from app.utils.product_validator import ProductQueryValidator

# ValidationResult is used internally by ProductQueryValidator
import asyncio
from typing import Optional, Dict, List
import time
import json
import warnings
import os

router = APIRouter()
logger = setup_logging()

import os
from dotenv import load_dotenv

load_dotenv()
BASE_DIRECTORY = os.getenv("BASE_DIRECTORY")


async def validate_request(
    query_product_ids: List[int] = Body(...),
    scope_product_id: Optional[List[int]] = Body([]),
    weights: Optional[Dict[str, float]] = Body({}),
    database: Optional[str] = Body(""),
):
    """Validate incoming request parameters using ProductQueryValidator"""
    log = logger.bind(product_ids=query_product_ids)

    processor = await registry.get_or_create(database)

    try:
        if not query_product_ids:
            return {"categories": []}

        # Prepare data for validator
        validation_data = {
            "query_ids": query_product_ids,
            "scope_ids": scope_product_id or [],
            "weights": {
                str(k): v for k, v in (weights or {}).items()
            },  # Convert keys to strings
        }

        # Get database path
        db_path = os.path.join(BASE_DIRECTORY, processor.pg)

        # Create and run validator
        validator = ProductQueryValidator(validation_data, db_path)
        result = validator.validate()

        # Handle validation errors
        if result.errors:
            error_msg = "; ".join(result.errors)
            log.error("request.validation.failed", errors=result.errors)
            raise HTTPException(status_code=400, detail=error_msg)

        # Handle validation warnings
        if result.warnings:
            for warning in result.warnings:
                log.warning("request.validation.warning", warning=warning)
                warnings.warn(warning, RuntimeWarning)

        # Extract validated data from validator
        valid_product_ids = [
            pid for pid in query_product_ids if pid in validator.product_ids
        ]
        valid_scope_ids = None
        if scope_product_id:
            valid_scope_ids = [
                pid for pid in scope_product_id if pid in validator.product_ids
            ]

        # Get category from validator
        category = validator.category_id

        # Process weights - remove invalid attribute IDs
        valid_atrids = weights or {}
        if weights and category is not None:
            category_str = str(category)
            if category_str in validator.attribute_id_by_category:
                valid_attr_ids = set(
                    map(str, validator.attribute_id_by_category[category_str])
                )
                valid_atrids = {
                    k: v for k, v in weights.items() if str(k) in valid_attr_ids
                }

        log.info("request.validation.success", category=category)
        return {
            "categories": category,
            "valid_product_ids": valid_product_ids,
            "valid_scope_ids": valid_scope_ids,
            "valid_atrids": valid_atrids,
        }

    except HTTPException:
        raise
    except Exception as e:
        log.error("request.validation.failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Validation failed: {str(e)}")


async def process_similar_products(
    query_product_ids: List[int],
    categories: List[int],
    database: Optional[str] = Body(None),
    weights: Optional[dict] = None,
    top_k: int = 5,
    scope_product_id: Optional[int] = None,
):
    processor = await registry.get_or_create(database)
    """Core processing function for similar products"""
    return await asyncio.to_thread(
        processor.inference.search_with_exist_products,
        query_product_ids,
        categories,
        weights,
        top_k,
        scope_product_id,
    )


@router.post("/get-similar-products-by-product-id")
async def get_similar_products(
    query_product_ids: List[int],
    database: Optional[str] = Body(default=""),
    weights: Optional[Dict[str, float]] = None,
    top_k: int = 5,
    scope_product_id: Optional[List[int]] = None,
    request_data: dict = Depends(validate_request),
):
    valid_product_ids = request_data["valid_product_ids"]
    valid_scope_ids = request_data["valid_scope_ids"]
    weights = request_data["valid_atrids"]
    processor = await registry.get_or_create(database)
    if weights is not None and len(weights) > 0:
        weights = {int(k): v for k, v in weights.items()}
    else:
        weights = None

    log = logger.bind(
        product_ids=valid_product_ids,
        categories=request_data["categories"],
        top_k=top_k,
    )
    category = request_data["categories"]
    start_time = time.time()
    try:
        products = await processor.queue_request(
            process_similar_products,
            valid_product_ids,
            category,
            database,
            weights,
            top_k,
            valid_scope_ids,
        )

        process_time = time.time() - start_time
        log.info(
            "similar_products.success",
            process_time=f"{process_time:.4f}s",
            results_count=len(products),
            result=products,
        )

        return JSONResponse(content=jsonable_encoder(products), status_code=200)

    except Exception as e:
        process_time = time.time() - start_time
        log.error(
            "similar_products.failed", error=str(e), process_time=f"{process_time:.4f}s"
        )
        raise


@router.post("/get-similar-products-by-specs")
async def get_similar_products_by_specs(
    specs: list[dict[str, str]],
    product_category_id: int,
    weights: Optional[dict] = None,
    top_k: int = 5,
    scope_query: Optional[str] = None,
    _: None = Depends(validate_request),
):
    # Implementation for specs-based search
    raise HTTPException(
        status_code=501, detail="Specs-based search not implemented yet"
    )


@router.get("/get-available-databases")
async def get_available_databases():
    with open(os.path.join(BASE_DIRECTORY, "databases.json"), "r") as f:
        databases = json.load(f)
    return JSONResponse(content=databases, status_code=200)


@router.get("/get-specific-search-scope-databases")
async def get_specific_search_scope_databases():
    pass
